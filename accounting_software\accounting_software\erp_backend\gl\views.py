"""
General Ledger Views for ERP Accounting Software

These views handle all General Ledger API endpoints with proper business logic,
permissions, and data validation. Following accounting best practices and
supporting both QuickBooks-style functionality and IFRS compliance.

Key Features:
- Chart of Accounts management with hierarchical structure
- Journal entry creation, editing, and posting workflow
- Account type and detail type management
- Comprehensive filtering and search capabilities
- Proper audit trail and permission controls
"""

from rest_framework import viewsets, status, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, IsAdminUser
from django_filters.rest_framework import DjangoFilterBackend
from django.db.models import Q, Sum, Count, Case, When, DecimalField, F
from django.utils import timezone
from decimal import Decimal, InvalidOperation
from datetime import date, datetime
import csv
import io
import tempfile
import os
from django.contrib.auth.decorators import login_required
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.db import transaction

from .models import (
    AccountType, DetailType, Account, JournalEntry, JournalEntryLine,
    Project, Product, VoucherType, RecurringJournalEntry, RecurringJournalEntryLine,
    AccountMove, AccountMoveLine, AccountJournal, IrSequence
)
from .serializers import (
    AccountTypeSerializer, DetailTypeSerializer, AccountSerializer,
    JournalEntrySerializer, JournalEntryLineSerializer, JournalEntryPostSerializer,
    ProjectSerializer, ProductSerializer, VoucherTypeSerializer,
    EnhancedJournalEntrySerializer, EnhancedJournalEntryLineSerializer,
    RecurringJournalEntrySerializer, RecurringJournalEntryLineSerializer,
    SalesTaxSerializer, TDSSerializer,
    AccountMoveSerializer, AccountMoveLineSerializer,
    AccountJournalSerializer, IrSequenceSerializer
)
from sales_tax.models import SalesTax
from tds.models import TDS
from account.models import Company

class AccountTypeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Account Type management (Assets, Liabilities, etc.)
    
    Account types are the fundamental building blocks of the Chart of Accounts.
    They determine:
    - Normal balance (debit or credit)
    - Financial statement presentation
    - Account behavior rules
    
    Only superusers can create/modify account types to maintain system integrity.
    """
    
    queryset = AccountType.objects.all().order_by('sort_order', 'name')
    serializer_class = AccountTypeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'financial_statement', 'is_active']
    search_fields = ['name', 'code']
    ordering_fields = ['sort_order', 'name', 'created_at']
    ordering = ['sort_order', 'name']
    
    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def destroy(self, request, *args, **kwargs):
        """Prevent deletion of account types that have accounts."""
        account_type = self.get_object()
        
        if account_type.accounts.exists():
            return Response(
                {
                    "detail": f"Cannot delete account type '{account_type.name}' because it has {account_type.accounts.count()} accounts assigned to it."
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)

class DetailTypeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Detail Type management (QuickBooks-style classifications)
    
    Detail types provide more granular categorization within account types:
    - Cash & Bank accounts under Assets
    - Travel & Entertainment under Expenses
    - Accounts Payable under Liabilities
    
    This enables better reporting and automated account behavior.
    """
    
    queryset = DetailType.objects.select_related('account_type').order_by(
        'account_type__sort_order', 'sort_order', 'name'
    )
    serializer_class = DetailTypeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['account_type', 'is_active', 'is_tax_account']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['sort_order', 'name', 'created_at']
    ordering = ['account_type__sort_order', 'sort_order', 'name']
    
    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def destroy(self, request, *args, **kwargs):
        """Prevent deletion of detail types that have accounts."""
        detail_type = self.get_object()
        
        if detail_type.accounts.exists():
            return Response(
                {
                    "detail": f"Cannot delete detail type '{detail_type.name}' because it has {detail_type.accounts.count()} accounts assigned to it."
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)
    
    @action(detail=False, methods=['get'])
    def by_account_type(self, request):
        """Get detail types grouped by account type."""
        account_type_id = request.query_params.get('account_type')
        
        if account_type_id:
            detail_types = self.queryset.filter(
                account_type_id=account_type_id,
                is_active=True
            )
        else:
            detail_types = self.queryset.filter(is_active=True)
        
        serializer = self.get_serializer(detail_types, many=True)
        return Response(serializer.data)

class AccountViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Chart of Accounts management
    
    The Chart of Accounts is the foundation of the accounting system.
    This endpoint handles:
    - Creating and managing individual accounts
    - Hierarchical parent/child relationships
    - Account balance calculations
    - Integration with banking systems
    - Multi-currency support
    
    Business Rules:
    - Account numbers must be unique
    - Detail type must match account type
    - Header accounts cannot have transactions
    - Parent accounts must be of same type or header accounts
    """
    
    queryset = Account.objects.select_related(
        'account_type', 'detail_type', 'parent_account', 'created_by'
    ).prefetch_related('child_accounts').order_by('account_number')
    
    serializer_class = AccountSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'account_type', 'detail_type', 'parent_account', 
        'is_active', 'is_header_account', 'currency'
    ]
    search_fields = ['account_number', 'account_name', 'description']
    ordering_fields = ['account_number', 'account_name', 'created_at']
    ordering = ['account_number']
    
    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def perform_create(self, serializer):
        """Set the created_by field when creating an account."""
        serializer.save(created_by=self.request.user)
    
    def destroy(self, request, *args, **kwargs):
        """Prevent deletion of accounts with transactions or child accounts."""
        account = self.get_object()
        
        # Check for child accounts
        if account.child_accounts.exists():
            return Response(
                {
                    "detail": f"Cannot delete account '{account.account_name}' because it has {account.child_accounts.count()} child accounts."
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check for transactions
        if account.journal_lines.exists():
            return Response(
                {
                    "detail": f"Cannot delete account '{account.account_name}' because it has transaction history."
                },
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)
    
    @action(detail=False, methods=['get'])
    def hierarchy(self, request):
        """Get accounts in hierarchical structure (tree view)."""
        # Get top-level accounts (no parent)
        top_level_accounts = self.queryset.filter(
            parent_account__isnull=True,
            is_active=True
        )
        
        # Filter by account type if specified
        account_type = request.query_params.get('account_type')
        if account_type:
            top_level_accounts = top_level_accounts.filter(account_type_id=account_type)
        
        serializer = self.get_serializer(top_level_accounts, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def balance(self, request, pk=None):
        """Get current balance for a specific account."""
        account = self.get_object()
        as_of_date = request.query_params.get('as_of_date')
        
        if as_of_date:
            try:
                from datetime import datetime
                as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        balance = account.get_current_balance(as_of_date)
        
        return Response({
            'account_number': account.account_number,
            'account_name': account.account_name,
            'balance': str(balance),
            'as_of_date': as_of_date or timezone.now().date(),
            'currency': account.currency
        })
    
    @action(detail=False, methods=['get'])
    def trial_balance(self, request):
        """Generate trial balance report with optimized database queries and comparative support."""
        as_of_date = request.query_params.get('as_of_date')
        include_zero_balances = request.query_params.get('include_zero_balances', 'false').lower() == 'true'
        include_comparatives = request.query_params.get('include_comparatives', 'false').lower() == 'true'
        comparative_date = request.query_params.get('comparative_date')
        
        if as_of_date:
            try:
                from datetime import datetime
                as_of_date = datetime.strptime(as_of_date, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {"detail": "Invalid date format. Use YYYY-MM-DD."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        else:
            as_of_date = timezone.now().date()
        
        # Handle comparative date
        if include_comparatives and not comparative_date:
            # Default to same date previous year if not specified
            comparative_date = as_of_date.replace(year=as_of_date.year - 1)
        elif comparative_date:
            try:
                from datetime import datetime
                comparative_date = datetime.strptime(comparative_date, '%Y-%m-%d').date()
            except ValueError:
                return Response(
                    {"detail": "Invalid comparative date format. Use YYYY-MM-DD."},
                    status=status.HTTP_400_BAD_REQUEST
                )
        
        # OPTIMIZED: Single database query to get all account balances
        from .models import JournalEntryLine
        
        # Subquery to calculate total debits and credits for each account
        account_totals = self.queryset.filter(
            is_active=True,
            is_header_account=False
        ).annotate(
            # Calculate total debits from posted journal entries up to as_of_date
            total_debits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__lte=as_of_date) &
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__debit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            # Calculate total credits from posted journal entries up to as_of_date
            total_credits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__lte=as_of_date) &
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__credit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            # Calculate current balance based on account type
            current_balance=Case(
                # For DEBIT normal balance accounts (Assets, Expenses)
                When(
                    account_type__normal_balance='DEBIT',
                    then=F('opening_balance') + F('total_debits') - F('total_credits')
                ),
                # For CREDIT normal balance accounts (Liabilities, Equity, Revenue)
                default=F('opening_balance') + F('total_credits') - F('total_debits'),
                output_field=DecimalField(max_digits=15, decimal_places=2)
            )
        ).select_related('account_type', 'detail_type')
        
        # Calculate comparative balances if requested
        comparative_balances = {}
        if include_comparatives and comparative_date:
            comparative_accounts = self.queryset.filter(
                is_active=True,
                is_header_account=False
            ).annotate(
                # Calculate total debits from posted journal entries up to comparative_date
                comp_total_debits=Sum(
                    Case(
                        When(
                            Q(journal_lines__journal_entry__transaction_date__lte=comparative_date) &
                            Q(journal_lines__journal_entry__status='POSTED'),
                            then='journal_lines__debit_amount'
                        ),
                        default=Decimal('0.00'),
                        output_field=DecimalField(max_digits=15, decimal_places=2)
                    )
                ),
                # Calculate total credits from posted journal entries up to comparative_date
                comp_total_credits=Sum(
                    Case(
                        When(
                            Q(journal_lines__journal_entry__transaction_date__lte=comparative_date) &
                            Q(journal_lines__journal_entry__status='POSTED'),
                            then='journal_lines__credit_amount'
                        ),
                        default=Decimal('0.00'),
                        output_field=DecimalField(max_digits=15, decimal_places=2)
                    )
                ),
                # Calculate comparative balance based on account type
                comp_balance=Case(
                    # For DEBIT normal balance accounts (Assets, Expenses)
                    When(
                        account_type__normal_balance='DEBIT',
                        then=F('opening_balance') + F('comp_total_debits') - F('comp_total_credits')
                    ),
                    # For CREDIT normal balance accounts (Liabilities, Equity, Revenue)
                    default=F('opening_balance') + F('comp_total_credits') - F('comp_total_debits'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            )
            
            for account in comparative_accounts:
                comparative_balances[account.id] = account.comp_balance or Decimal('0.00')
        
        # Filter accounts with zero balances if requested
        if not include_zero_balances:
            if include_comparatives:
                # For comparative TB, exclude accounts that have zero in both periods
                account_totals = account_totals.exclude(
                    Q(current_balance=0) & Q(opening_balance=0) & 
                    Q(total_debits=0) & Q(total_credits=0)
                ).filter(
                    # Keep accounts that have activity in either period
                    Q(current_balance__ne=0) | 
                    Q(id__in=list(comparative_balances.keys()))
                )
            else:
                account_totals = account_totals.exclude(
                    Q(current_balance=0) & Q(opening_balance=0) & 
                    Q(total_debits=0) & Q(total_credits=0)
                )
        
        # Order by account number
        account_totals = account_totals.order_by('account_number')
        
        # Build trial balance data
        trial_balance_data = []
        total_debits = Decimal('0.00')
        total_credits = Decimal('0.00')
        comp_total_debits = Decimal('0.00')
        comp_total_credits = Decimal('0.00')
        
        for account in account_totals:
            balance = account.current_balance or Decimal('0.00')
            comp_balance = comparative_balances.get(account.id, Decimal('0.00')) if include_comparatives else None
            
            # Determine debit/credit presentation based on normal balance and actual balance
            if account.account_type.normal_balance == 'DEBIT':
                # Assets and Expenses - positive balances are debits
                debit_balance = balance if balance >= 0 else Decimal('0.00')
                credit_balance = abs(balance) if balance < 0 else Decimal('0.00')
                
                if comp_balance is not None:
                    comp_debit_balance = comp_balance if comp_balance >= 0 else Decimal('0.00')
                    comp_credit_balance = abs(comp_balance) if comp_balance < 0 else Decimal('0.00')
                else:
                    comp_debit_balance = comp_credit_balance = None
            else:
                # Liabilities, Equity, Revenue - positive balances are credits
                credit_balance = balance if balance >= 0 else Decimal('0.00')
                debit_balance = abs(balance) if balance < 0 else Decimal('0.00')
                
                if comp_balance is not None:
                    comp_credit_balance = comp_balance if comp_balance >= 0 else Decimal('0.00')
                    comp_debit_balance = abs(comp_balance) if comp_balance < 0 else Decimal('0.00')
                else:
                    comp_debit_balance = comp_credit_balance = None
            
            account_data = {
                'account_id': account.id,
                'account_number': account.account_number,
                'account_name': account.account_name,
                'account_type': account.account_type.name,
                'account_type_code': account.account_type.code,
                'detail_type': account.detail_type.name,
                'normal_balance': account.account_type.normal_balance,
                'opening_balance': account.opening_balance,
                'total_debits': account.total_debits or Decimal('0.00'),
                'total_credits': account.total_credits or Decimal('0.00'),
                'current_balance': balance,
                'debit_balance': debit_balance,
                'credit_balance': credit_balance,
            }
            
            # Add comparative data if requested
            if include_comparatives:
                account_data.update({
                    'comparative_balance': comp_balance,
                    'comparative_debit_balance': comp_debit_balance,
                    'comparative_credit_balance': comp_credit_balance,
                    'variance': balance - comp_balance if comp_balance is not None else None,
                    'variance_percent': (
                        ((balance - comp_balance) / abs(comp_balance) * 100)
                        if comp_balance and comp_balance != 0 else None
                    )
                })
                
                if comp_debit_balance is not None:
                    comp_total_debits += comp_debit_balance
                if comp_credit_balance is not None:
                    comp_total_credits += comp_credit_balance
            
            trial_balance_data.append(account_data)
            
            total_debits += debit_balance
            total_credits += credit_balance
        
        # Calculate summary by account type
        type_summary = {}
        for account in trial_balance_data:
            account_type = account['account_type']
            if account_type not in type_summary:
                type_summary[account_type] = {
                    'account_type': account_type,
                    'account_type_code': account['account_type_code'],
                    'normal_balance': account['normal_balance'],
                    'total_debit_balance': Decimal('0.00'),
                    'total_credit_balance': Decimal('0.00'),
                    'account_count': 0
                }
            
            type_summary[account_type]['total_debit_balance'] += account['debit_balance']
            type_summary[account_type]['total_credit_balance'] += account['credit_balance']
            type_summary[account_type]['account_count'] += 1
        
        response_data = {
            'as_of_date': as_of_date,
            'company_name': 'Your Company Name',  # TODO: Get from company settings
            'report_title': 'Trial Balance' + (' with Comparatives' if include_comparatives else ''),
            'accounts': trial_balance_data,
            'account_type_summary': list(type_summary.values()),
            'total_debits': total_debits,
            'total_credits': total_credits,
            'is_balanced': abs(total_debits - total_credits) < Decimal('0.01'),
            'variance': total_debits - total_credits,
            'total_accounts': len(trial_balance_data),
            'include_zero_balances': include_zero_balances,
            'include_comparatives': include_comparatives,
            'generated_at': timezone.now(),
        }
        
        # Add comparative data to response if requested
        if include_comparatives:
            response_data.update({
                'comparative_date': comparative_date,
                'comparative_total_debits': comp_total_debits,
                'comparative_total_credits': comp_total_credits,
                'comparative_is_balanced': abs(comp_total_debits - comp_total_credits) < Decimal('0.01'),
                'comparative_variance': comp_total_debits - comp_total_credits,
            })
        
        return Response(response_data)

class JournalEntryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Journal Entry management
    
    Journal entries are the core of double-entry bookkeeping. This endpoint
    handles the complete lifecycle:
    - Creating draft entries
    - Editing and validation
    - Posting to the general ledger
    - Reversal entries
    - Comprehensive audit trail
    
    Business Rules:
    - Debits must equal credits
    - Minimum 2 lines required
    - Posted entries cannot be modified
    - Proper workflow status management
    """
    
    queryset = JournalEntry.objects.select_related(
        'created_by', 'posted_by'
    ).prefetch_related(
        'journal_lines__account'
    ).order_by('-transaction_date', '-entry_number')
    
    serializer_class = JournalEntrySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'status', 'entry_type', 'currency', 'created_by', 'posted_by'
    ]
    search_fields = ['entry_number', 'description', 'reference_number', 'memo']
    ordering_fields = ['transaction_date', 'entry_number', 'created_at']
    ordering = ['-transaction_date', '-entry_number']
    
    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['create', 'update', 'partial_update']:
            permission_classes = [IsAuthenticated]
        elif self.action in ['destroy', 'post_entry', 'reverse_entry']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def perform_create(self, serializer):
        """Set the created_by field when creating a journal entry."""
        # Check for duplicate source documents
        source_document_type = serializer.validated_data.get('source_document_type')
        source_document_id = serializer.validated_data.get('source_document_id')
        
        if source_document_type and source_document_id:
            # Check if there's already a journal entry for this source document
            existing_entry = JournalEntry.objects.filter(
                source_document_type=source_document_type,
                source_document_id=source_document_id
            ).exclude(status='REVERSED').first()
            
            if existing_entry:
                from rest_framework.exceptions import ValidationError
                raise ValidationError(
                    f"A journal voucher already exists for {source_document_type} {source_document_id}. "
                    f"Journal Entry: {existing_entry.entry_number} created on {existing_entry.created_at.strftime('%Y-%m-%d')}. "
                    f"Each {source_document_type} can only be converted to a journal voucher once."
                )
        
        serializer.save(created_by=self.request.user)
    
    def update(self, request, *args, **kwargs):
        """Prevent modification of posted entries."""
        instance = self.get_object()
        
        if instance.status == 'POSTED':
            return Response(
                {"detail": "Cannot modify posted journal entries"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().update(request, *args, **kwargs)
    
    def destroy(self, request, *args, **kwargs):
        """Only allow deletion of draft entries."""
        instance = self.get_object()
        
        if instance.status != 'DRAFT':
            return Response(
                {"detail": "Only draft journal entries can be deleted"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        return super().destroy(request, *args, **kwargs)
    
    @action(detail=True, methods=['post'])
    def post_entry(self, request, pk=None):
        """Post a journal entry to the general ledger."""
        journal_entry = self.get_object()
        
        # Validate posting requirements
        if not journal_entry.can_be_posted():
            errors = []
            if journal_entry.status != 'DRAFT':
                errors.append(f"Entry status is '{journal_entry.get_status_display()}', must be 'Draft'")
            if not journal_entry.is_balanced():
                errors.append("Total debits must equal total credits")
            if journal_entry.journal_lines.count() < 2:
                errors.append("Entry must have at least 2 lines")
            
            return Response(
                {"detail": "Cannot post journal entry", "errors": errors},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Use serializer for validation
        serializer = JournalEntryPostSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Post the entry
            journal_entry.post_entry(request.user)
            
            # Return updated entry
            response_serializer = self.get_serializer(journal_entry)
            return Response({
                "detail": "Journal entry posted successfully",
                "journal_entry": response_serializer.data
            })
            
        except Exception as e:
            return Response(
                {"detail": f"Error posting journal entry: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def reverse_entry(self, request, pk=None):
        """Create a reversing entry for the specified journal entry."""
        original_entry = self.get_object()
        
        if original_entry.status != 'POSTED':
            return Response(
                {"detail": "Only posted entries can be reversed"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        if JournalEntry.objects.filter(reversed_entry=original_entry).exists():
            return Response(
                {"detail": "This entry has already been reversed"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Create reversing entry
            reversing_entry = JournalEntry.objects.create(
                entry_number=f"REV-{original_entry.entry_number}",
                transaction_date=timezone.now().date(),
                entry_type='REVERSING',
                description=f"Reversal of {original_entry.entry_number}: {original_entry.description}",
                reference_number=original_entry.reference_number,
                memo=request.data.get('reversal_reason', ''),
                currency=original_entry.currency,
                exchange_rate=original_entry.exchange_rate,
                reversed_entry=original_entry,
                reversal_reason=request.data.get('reversal_reason', ''),
                created_by=request.user
            )
            
            # Create reversing lines (swap debits and credits)
            for line in original_entry.journal_lines.all():
                JournalEntryLine.objects.create(
                    journal_entry=reversing_entry,
                    account=line.account,
                    debit_amount=line.credit_amount,  # Swap
                    credit_amount=line.debit_amount,  # Swap
                    description=f"Reversal: {line.description}",
                    memo=line.memo,
                    customer=line.customer,
                    vendor=line.vendor,
                    project=line.project,
                    department=line.department,
                    location=line.location,
                    line_number=line.line_number
                )
            
            # Auto-post the reversing entry
            reversing_entry.post_entry(request.user)
            
            # Return the reversing entry
            response_serializer = self.get_serializer(reversing_entry)
            return Response({
                "detail": "Reversing entry created and posted successfully",
                "reversing_entry": response_serializer.data
            })
            
        except Exception as e:
            return Response(
                {"detail": f"Error creating reversing entry: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    

    
    @action(detail=False, methods=['get'])
    def draft_entries(self, request):
        """Get all draft journal entries for the current user."""
        draft_entries = self.queryset.filter(
            status='DRAFT',
            created_by=request.user
        )
        
        serializer = self.get_serializer(draft_entries, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def pending_entries(self, request):
        """Get all pending journal entries for review."""
        pending_entries = self.queryset.filter(status='PENDING')
        
        page = self.paginate_queryset(pending_entries)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(pending_entries, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def copy_entry(self, request, pk=None):
        """Create a copy of an existing journal entry."""
        original_entry = self.get_object()
        
        # Get new entry details from request
        new_entry_number = request.data.get('entry_number')
        new_transaction_date = request.data.get('transaction_date')
        new_description = request.data.get('description')
        
        if not new_entry_number:
            return Response(
                {"detail": "New entry number is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if entry number already exists
        if JournalEntry.objects.filter(entry_number=new_entry_number).exists():
            return Response(
                {"detail": f"Journal Entry number '{new_entry_number}' already exists. Please use a different entry number."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Additional validation: Check if trying to copy with same entry number as original
        if new_entry_number == original_entry.entry_number:
            return Response(
                {"detail": f"Cannot copy entry with the same entry number '{new_entry_number}'. Please provide a different entry number."},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            with transaction.atomic():
                # Create new journal entry
                new_entry = JournalEntry.objects.create(
                    entry_number=new_entry_number,
                    transaction_date=new_transaction_date or original_entry.transaction_date,
                    description=new_description or f"Copy of {original_entry.description}",
                    reference_number=original_entry.reference_number,
                    memo=original_entry.memo,
                    entry_type=original_entry.entry_type,
                    currency=original_entry.currency,
                    exchange_rate=original_entry.exchange_rate,
                    status='DRAFT',  # Always create as draft
                    created_by=request.user
                )
                
                # Copy all journal lines
                for original_line in original_entry.journal_lines.all():
                    JournalEntryLine.objects.create(
                        journal_entry=new_entry,
                        account=original_line.account,
                        debit_amount=original_line.debit_amount,
                        credit_amount=original_line.credit_amount,
                        description=original_line.description,
                        memo=original_line.memo,
                        customer=original_line.customer,
                        vendor=original_line.vendor,
                        project=original_line.project,
                        product=original_line.product,
                        department=original_line.department,
                        location=original_line.location,
                        line_number=original_line.line_number
                    )
                
                # Return the new entry
                serializer = self.get_serializer(new_entry)
                return Response({
                    "detail": "Journal entry copied successfully",
                    "new_entry": serializer.data
                })
                
        except Exception as e:
            return Response(
                {"detail": f"Error copying journal entry: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

class ProjectViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Project/Job management
    
    Projects allow detailed tracking of transactions for better
    cost allocation and project profitability analysis.
    """
    
    queryset = Project.objects.select_related('created_by').order_by('project_code')
    serializer_class = ProjectSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active', 'created_by']
    search_fields = ['project_code', 'project_name', 'description']
    ordering_fields = ['project_code', 'project_name', 'created_at']
    ordering = ['project_code']
    
    def perform_create(self, serializer):
        """Set the created_by field when creating a project."""
        serializer.save(created_by=self.request.user)

class ProductViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Product/Service management
    
    Products allow detailed tracking of transactions for better
    profitability analysis and inventory management.
    """
    
    queryset = Product.objects.select_related('created_by').order_by('product_code')
    serializer_class = ProductSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['product_type', 'is_active', 'created_by']
    search_fields = ['product_code', 'product_name', 'description']
    ordering_fields = ['product_code', 'product_name', 'created_at']
    ordering = ['product_code']
    
    def perform_create(self, serializer):
        """Set the created_by field when creating a product."""
        serializer.save(created_by=self.request.user)

class VoucherTypeViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Voucher Type management
    
    Voucher types define different forms for journal entries
    with specific numbering and behavior patterns.
    """
    
    queryset = VoucherType.objects.all().order_by('voucher_code')
    serializer_class = VoucherTypeSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['voucher_code', 'is_active']
    search_fields = ['voucher_name', 'description']
    ordering_fields = ['voucher_code', 'voucher_name']
    ordering = ['voucher_code']
    
    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['create', 'update', 'partial_update', 'destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]

class SalesTaxViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Read-only API endpoint for Sales Tax dropdown lists
    """
    
    queryset = SalesTax.objects.all().order_by('tax_type', 'description')
    serializer_class = SalesTaxSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None  # Disable pagination for dropdown lists
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['tax_type']

class TDSViewSet(viewsets.ReadOnlyModelViewSet):
    """
    Read-only API endpoint for TDS dropdown lists
    """
    
    queryset = TDS.objects.all().order_by('tds_type', 'description')
    serializer_class = TDSSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None  # Disable pagination for dropdown lists
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['tds_type']

class EnhancedJournalEntryViewSet(viewsets.ModelViewSet):
    """
    Enhanced API endpoint for Journal Entry management with tax calculations
    
    This enhanced version includes:
    - Voucher type support (JV, PV, RV, BV, CV)
    - Automatic tax calculations (Sales Tax and TDS)
    - Product/Project tracking
    - Enhanced validation and business logic
    """
    
    queryset = JournalEntry.objects.select_related(
        'created_by', 'posted_by', 'voucher_type', 'input_tax', 'output_tax', 'tds_rate'
    ).prefetch_related(
        'journal_lines__account', 'journal_lines__project', 'journal_lines__product'
    ).order_by('-transaction_date', '-entry_number')
    
    serializer_class = EnhancedJournalEntrySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = [
        'status', 'entry_type', 'voucher_type', 'currency', 'created_by', 'posted_by'
    ]
    search_fields = ['entry_number', 'description', 'reference_number', 'memo']
    ordering_fields = ['transaction_date', 'entry_number', 'created_at']
    ordering = ['-transaction_date', '-entry_number']
    
    def get_permissions(self):
        """Set permissions based on the action being performed."""
        if self.action in ['destroy']:
            permission_classes = [IsAdminUser]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]
    
    def perform_create(self, serializer):
        """Set the created_by field when creating a journal entry."""
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def calculate_taxes(self, request, pk=None):
        """Manually trigger tax calculation for a journal entry."""
        journal_entry = self.get_object()
        
        if journal_entry.status == 'POSTED':
            return Response(
                {"detail": "Cannot recalculate taxes for posted entries"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        journal_entry.calculate_taxes()
        journal_entry.save()
        
        serializer = self.get_serializer(journal_entry)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def generate_tax_lines(self, request, pk=None):
        """Generate automatic tax journal lines."""
        journal_entry = self.get_object()
        
        if journal_entry.status == 'POSTED':
            return Response(
                {"detail": "Cannot modify posted entries"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Calculate taxes first
        journal_entry.calculate_taxes()
        journal_entry.save()
        
        # Generate tax lines
        tax_lines = journal_entry.generate_tax_journal_lines()
        
        # Create the lines
        for line_data in tax_lines:
            line_data['journal_entry'] = journal_entry
            JournalEntryLine.objects.create(**line_data)
        
        serializer = self.get_serializer(journal_entry)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def by_voucher_type(self, request):
        """Get journal entries filtered by voucher type."""
        voucher_code = request.query_params.get('voucher_code')
        
        if voucher_code:
            entries = self.queryset.filter(voucher_type__voucher_code=voucher_code)
        else:
            entries = self.queryset.all()
        
        page = self.paginate_queryset(entries)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(entries, many=True)
        return Response(serializer.data)

class RecurringJournalEntryViewSet(viewsets.ModelViewSet):
    """
    API endpoint for Recurring Journal Entry Templates
    
    This endpoint manages templates for journal entries that need to be created
    repeatedly on a schedule (monthly rent, quarterly insurance, etc.).
    
    Features:
    - Create and manage recurring templates
    - Generate journal entries from templates
    - Schedule management (daily, weekly, monthly, quarterly, yearly)
    - Template validation and balance checking
    """
    
    queryset = RecurringJournalEntry.objects.select_related(
        'created_by'
    ).prefetch_related(
        'recurring_lines__account'
    ).order_by('-created_at')
    
    serializer_class = RecurringJournalEntrySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['frequency', 'is_active', 'created_by']
    search_fields = ['template_name', 'description', 'reference_number']
    ordering_fields = ['template_name', 'frequency', 'start_date', 'created_at']
    ordering = ['-created_at']
    
    def perform_create(self, serializer):
        """Set the created_by field when creating a recurring template."""
        serializer.save(created_by=self.request.user)
    
    @action(detail=True, methods=['post'])
    def generate_entry(self, request, pk=None):
        """Generate a journal entry from this recurring template."""
        recurring_template = self.get_object()
        
        if not recurring_template.is_active:
            return Response(
                {"detail": "Cannot generate entries from inactive templates"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if template is balanced
        total_debits = sum(line.debit_amount for line in recurring_template.recurring_lines.all())
        total_credits = sum(line.credit_amount for line in recurring_template.recurring_lines.all())
        
        if abs(total_debits - total_credits) >= Decimal('0.01'):
            return Response(
                {"detail": "Cannot generate entries from unbalanced templates"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            # Get generation date from request or use today
            generation_date_str = request.data.get('generation_date')
            if generation_date_str:
                from datetime import datetime
                generation_date = datetime.strptime(generation_date_str, '%Y-%m-%d').date()
            else:
                generation_date = None
            
            # Generate the journal entry
            journal_entry = recurring_template.generate_journal_entry(generation_date)
            
            # Return the generated journal entry
            from .serializers import JournalEntrySerializer
            journal_serializer = JournalEntrySerializer(journal_entry)
            
            return Response({
                "detail": "Journal entry generated successfully",
                "journal_entry": journal_serializer.data,
                "template_stats": {
                    "total_generated": recurring_template.total_generated,
                    "last_generated_at": recurring_template.last_generated_at,
                    "next_generation_date": recurring_template.next_generation_date
                }
            })
            
        except Exception as e:
            return Response(
                {"detail": f"Error generating journal entry: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=False, methods=['get'])
    def active_templates(self, request):
        """Get all active recurring templates."""
        active_templates = self.queryset.filter(is_active=True)
        
        page = self.paginate_queryset(active_templates)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(active_templates, many=True)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def due_for_generation(self, request):
        """Get templates that are due for generation."""
        from datetime import date
        
        due_templates = self.queryset.filter(
            is_active=True,
            next_generation_date__lte=date.today()
        )
        
        serializer = self.get_serializer(due_templates, many=True)
        return Response({
            'templates_due': serializer.data,
            'count': len(due_templates),
            'as_of_date': date.today()
        })
    
    @action(detail=True, methods=['post'])
    def copy_template(self, request, pk=None):
        """Create a copy of an existing recurring template."""
        original_template = self.get_object()
        
        # Get new template name from request
        new_name = request.data.get('template_name')
        if not new_name:
            return Response(
                {"detail": "New template name is required"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        # Check if name already exists
        if RecurringJournalEntry.objects.filter(template_name__iexact=new_name).exists():
            return Response(
                {"detail": "A template with this name already exists"},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        try:
            with transaction.atomic():
                # Create new template
                new_template = RecurringJournalEntry.objects.create(
                    template_name=new_name,
                    description=f"Copy of {original_template.description}",
                    reference_number=original_template.reference_number,
                    memo=original_template.memo,
                    frequency=original_template.frequency,
                    start_date=request.data.get('start_date', original_template.start_date),
                    end_date=original_template.end_date,
                    currency=original_template.currency,
                    is_active=True,
                    created_by=request.user
                )
                
                # Calculate next generation date
                new_template.calculate_next_generation_date()
                new_template.save()
                
                # Copy all lines
                for original_line in original_template.recurring_lines.all():
                    RecurringJournalEntryLine.objects.create(
                        recurring_entry=new_template,
                        account=original_line.account,
                        debit_amount=original_line.debit_amount,
                        credit_amount=original_line.credit_amount,
                        description=original_line.description,
                        memo=original_line.memo,
                        line_number=original_line.line_number
                    )
                
                # Return the new template
                serializer = self.get_serializer(new_template)
                return Response({
                    "detail": "Template copied successfully",
                    "new_template": serializer.data
                })
                
        except Exception as e:
            return Response(
                {"detail": f"Error copying template: {str(e)}"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def company_currency_info(request):
    """
    API endpoint to get company currency information for journal entries.
    
    Returns:
    - functional_currency: Company's functional currency code
    - reporting_currency: Company's reporting currency code  
    - functional_currency_symbol: Symbol for functional currency
    - reporting_currency_symbol: Symbol for reporting currency
    - available_currencies: List of all supported currencies
    """
    try:
        company = Company.objects.first()
        if not company:
            return Response(
                {"detail": "No company configured"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Get available currencies from company choices
        available_currencies = [
            {
                'code': code,
                'name': name.split('(')[0].strip(),
                'symbol': name.split('(')[1].replace(')', '') if '(' in name else code
            }
            for code, name in Company.CURRENCY_CHOICES
        ]
        
        currency_info = {
            'functional_currency': company.functional_currency,
            'reporting_currency': company.reporting_currency,
            'functional_currency_symbol': company.get_currency_symbol('functional'),
            'reporting_currency_symbol': company.get_currency_symbol('reporting'),
            'available_currencies': available_currencies,
            'company_name': company.name
        }
        
        return Response(currency_info)
        
    except Exception as e:
        return Response(
            {"detail": f"Error fetching currency info: {str(e)}"}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def chart_of_accounts_fast(request):
    """
    Optimized Chart of Accounts endpoint - fetches all data in single query.
    
    This endpoint replaces the need for 3 separate API calls by fetching:
    - All accounts with related data
    - All account types
    - All detail types
    
    All in a single optimized database query with proper joins.
    
    Returns:
    {
        "accounts": [...],
        "account_types": [...], 
        "detail_types": [...],
        "metadata": {
            "total_accounts": 40,
            "active_accounts": 35,
            "transaction_accounts": 29,
            "load_time_ms": 45
        }
    }
    """
    import time
    start_time = time.time()
    
    try:
        # OPTIMIZED: Single query with all necessary joins
        accounts = Account.objects.select_related(
            'account_type', 'detail_type', 'parent_account', 'created_by'
        ).prefetch_related(
            'child_accounts'
        ).annotate(
            # Calculate current balance in database
            total_debits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__debit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            total_credits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__credit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            current_balance=Case(
                # For DEBIT normal balance accounts (Assets, Expenses)
                When(
                    account_type__normal_balance='DEBIT',
                    then=F('opening_balance') + F('total_debits') - F('total_credits')
                ),
                # For CREDIT normal balance accounts (Liabilities, Equity, Revenue)
                default=F('opening_balance') + F('total_credits') - F('total_debits'),
                output_field=DecimalField(max_digits=15, decimal_places=2)
            )
        ).order_by('account_number')
        
        # Get account types and detail types in single queries
        account_types = AccountType.objects.all().order_by('sort_order', 'name')
        detail_types = DetailType.objects.select_related('account_type').order_by(
            'account_type__sort_order', 'sort_order', 'name'
        )
        
        # Serialize data efficiently
        accounts_data = []
        for account in accounts:
            accounts_data.append({
                'id': account.id,
                'account_number': account.account_number,
                'account_name': account.account_name,
                'description': account.description,
                'account_type': account.account_type.id,
                'account_type_name': account.account_type.name,
                'account_type_code': account.account_type.code,
                'detail_type': account.detail_type.id,
                'detail_type_name': account.detail_type.name,
                'parent_account': account.parent_account.id if account.parent_account else None,
                'parent_account_name': account.parent_account.account_name if account.parent_account else None,
                'currency': account.currency,
                'is_active': account.is_active,
                'is_header_account': account.is_header_account,
                'opening_balance': str(account.opening_balance),
                'opening_balance_date': account.opening_balance_date,
                'current_balance': str(account.current_balance or Decimal('0.00')),
                'level': account.level,
                'path': account.path,
                'created_by': account.created_by.id,
                'created_by_username': account.created_by.username,
                'created_at': account.created_at,
                'updated_at': account.updated_at,
            })
        
        account_types_data = []
        for at in account_types:
            account_types_data.append({
                'id': at.id,
                'code': at.code,
                'name': at.name,
                'type': at.type,
                'normal_balance': at.normal_balance,
                'financial_statement': at.financial_statement,
                'sort_order': at.sort_order,
                'is_active': at.is_active,
            })
        
        detail_types_data = []
        for dt in detail_types:
            detail_types_data.append({
                'id': dt.id,
                'code': dt.code,
                'name': dt.name,
                'description': dt.description,
                'account_type': dt.account_type.id,
                'account_type_name': dt.account_type.name,
                'account_type_code': dt.account_type.code,
                'requires_customer': dt.requires_customer,
                'requires_vendor': dt.requires_vendor,
                'requires_item': dt.requires_item,
                'is_tax_account': dt.is_tax_account,
                'default_tax_code': dt.default_tax_code,
                'sort_order': dt.sort_order,
                'is_active': dt.is_active,
                'created_at': dt.created_at,
                'updated_at': dt.updated_at,
            })
        
        # Calculate statistics
        total_accounts = len(accounts_data)
        active_accounts = len([a for a in accounts_data if a['is_active']])
        transaction_accounts = len([a for a in accounts_data if a['is_active'] and not a['is_header_account']])
        
        end_time = time.time()
        load_time_ms = round((end_time - start_time) * 1000, 2)
        
        return Response({
            'accounts': accounts_data,
            'account_types': account_types_data,
            'detail_types': detail_types_data,
            'metadata': {
                'total_accounts': total_accounts,
                'active_accounts': active_accounts,
                'transaction_accounts': transaction_accounts,
                'load_time_ms': load_time_ms,
                'query_optimized': True,
                'database_queries': 3,  # accounts, account_types, detail_types
                'generated_at': timezone.now()
            }
        })
        
    except Exception as e:
        return Response(
            {"detail": f"Error loading Chart of Accounts: {str(e)}"}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def account_ledger(request, account_id):
    """
    Get detailed ledger entries for a specific account
    
    Returns all journal entry lines for the account with running balance calculations
    and comprehensive filtering options for date range and status.
    
    OPTIMIZED: Uses prefetch_related to minimize database queries from N+1 to just 4 queries total:
    1. Get account details
    2. Get opening balance aggregation  
    3. Get ledger lines with prefetched journal entries and accounts
    4. Database-level calculations only
    """
    import time
    start_time = time.time()
    
    try:
        # Get the account
        account = Account.objects.select_related(
            'account_type', 'detail_type'
        ).get(id=account_id)
        
        # Get filter parameters
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        status_filter = request.GET.get('status', 'all')
        
        # Build query for journal entry lines with optimized prefetching
        lines_query = JournalEntryLine.objects.filter(
            account=account
        ).select_related(
            'journal_entry', 'account'
        ).prefetch_related(
            'journal_entry__journal_lines__account'  # Prefetch all lines in each journal entry
        ).order_by('journal_entry__transaction_date', 'journal_entry__id', 'line_number')
        
        # Apply date filters
        if date_from:
            lines_query = lines_query.filter(journal_entry__transaction_date__gte=date_from)
        if date_to:
            lines_query = lines_query.filter(journal_entry__transaction_date__lte=date_to)
            
        # Apply status filter
        if status_filter != 'all':
            lines_query = lines_query.filter(journal_entry__status=status_filter)
        
        # Get opening balance (transactions before date_from)
        opening_balance = account.opening_balance
        if date_from:
            # Calculate balance from opening balance plus all transactions before date_from
            opening_lines = JournalEntryLine.objects.filter(
                account=account,
                journal_entry__transaction_date__lt=date_from,
                journal_entry__status='POSTED'
            )
            
            opening_debits = opening_lines.aggregate(
                total=Sum('debit_amount')
            )['total'] or Decimal('0.00')
            
            opening_credits = opening_lines.aggregate(
                total=Sum('credit_amount')
            )['total'] or Decimal('0.00')
            
            if account.account_type.normal_balance == 'DEBIT':
                opening_balance = account.opening_balance + opening_debits - opening_credits
            else:
                opening_balance = account.opening_balance + opening_credits - opening_debits
        
        # Process entries and calculate running balance with optimized contra account lookup
        ledger_entries = []
        running_balance = opening_balance
        total_debits = Decimal('0.00')
        total_credits = Decimal('0.00')
        
        for line in lines_query:
            # Calculate running balance
            if account.account_type.normal_balance == 'DEBIT':
                running_balance += line.debit_amount - line.credit_amount
            else:
                running_balance += line.credit_amount - line.debit_amount
            
            # Accumulate totals
            total_debits += line.debit_amount
            total_credits += line.credit_amount
            
            # Find contra accounts using prefetched data (NO additional DB query!)
            contra_accounts = [
                other_line.account.account_name 
                for other_line in line.journal_entry.journal_lines.all()
                if other_line.id != line.id
            ]
            contra_account = ', '.join(contra_accounts) if contra_accounts else None
            
            ledger_entries.append({
                'id': line.id,
                'transaction_date': line.journal_entry.transaction_date,
                'entry_number': line.journal_entry.entry_number,
                'description': line.description or line.journal_entry.description,
                'reference_number': line.journal_entry.reference_number,
                'debit_amount': float(line.debit_amount),
                'credit_amount': float(line.credit_amount),
                'running_balance': float(running_balance),
                'journal_entry_id': line.journal_entry.id,
                'journal_entry_status': line.journal_entry.status,
                'contra_account': contra_account,
                'memo': line.memo,
            })
        
        # Calculate closing balance
        closing_balance = running_balance
        
        # Calculate performance metrics
        end_time = time.time()
        load_time_ms = round((end_time - start_time) * 1000, 2)
        
        # Prepare response data
        response_data = {
            'account': {
                'id': account.id,
                'account_number': account.account_number,
                'account_name': account.account_name,
                'account_type': account.account_type.name,
                'account_type_name': account.account_type.name,
                'detail_type_name': account.detail_type.name,
                'current_balance': float(account.get_current_balance()),
                'is_active': account.is_active,
                'is_header_account': account.is_header_account,
                'normal_balance': account.account_type.normal_balance,
            },
            'entries': ledger_entries,
            'opening_balance': float(opening_balance),
            'closing_balance': float(closing_balance),
            'total_debits': float(total_debits),
            'total_credits': float(total_credits),
            'period_from': date_from or 'Beginning',
            'period_to': date_to or 'Current',
            'metadata': {
                'total_entries': len(ledger_entries),
                'account_type': account.account_type.name,
                'normal_balance': account.account_type.normal_balance,
                'load_time_ms': load_time_ms,
                'database_queries': 4,  # Optimized: account + opening_balance + lines + prefetched
                'optimized': True,
                'generated_at': timezone.now().isoformat(),
            }
        }
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Account.DoesNotExist:
        return Response(
            {'error': 'Account not found'}, 
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {'error': f'Failed to generate account ledger: {str(e)}'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def financial_statements(request):
    """
    Generate comprehensive financial statements with fiscal/calendar year support.
    
    Query Parameters:
    - statement_type: 'balance_sheet', 'income_statement', 'cash_flow', 'all'
    - period_type: 'fiscal_year', 'calendar_year', 'custom'
    - year: Year for fiscal/calendar year (e.g., 2024)
    - from_date: Start date for custom period (YYYY-MM-DD)
    - to_date: End date for custom period (YYYY-MM-DD)
    - currency: 'functional', 'reporting', or specific currency code
    - include_comparatives: 'true'/'false' - include previous period comparison
    """
    try:
        # Get company information for fiscal/calendar year settings
        company = Company.objects.first()
        if not company:
            return Response(
                {"detail": "No company configured"}, 
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Parse query parameters
        statement_type = request.query_params.get('statement_type', 'all')
        period_type = request.query_params.get('period_type', 'fiscal_year')
        year = int(request.query_params.get('year', timezone.now().year))
        currency = request.query_params.get('currency', 'functional')
        include_comparatives = request.query_params.get('include_comparatives', 'false').lower() == 'true'
        
        # Calculate period dates based on type
        if period_type == 'fiscal_year':
            # Use company's fiscal year settings
            period_start = company.fiscal_year_start.replace(year=year)
            period_end = company.fiscal_year_end.replace(year=year)
            
            # Adjust for fiscal years that span calendar years
            # Check if fiscal year end is in the following calendar year
            if (company.fiscal_year_end.month < company.fiscal_year_start.month) or \
               (company.fiscal_year_end.month == company.fiscal_year_start.month and 
                company.fiscal_year_end.day < company.fiscal_year_start.day):
                period_end = period_end.replace(year=year + 1)
        elif period_type == 'calendar_year':
            period_start = date(year, 1, 1)
            period_end = date(year, 12, 31)
        else:  # custom
            period_start = datetime.strptime(
                request.query_params.get('from_date', f'{year}-01-01'), 
                '%Y-%m-%d'
            ).date()
            period_end = datetime.strptime(
                request.query_params.get('to_date', f'{year}-12-31'), 
                '%Y-%m-%d'
            ).date()
        
        # Calculate comparative period (previous year)
        comparative_start = None
        comparative_end = None
        if include_comparatives:
            if period_type == 'fiscal_year':
                comparative_start = company.fiscal_year_start.replace(year=year - 1)
                comparative_end = company.fiscal_year_end.replace(year=year - 1)
                
                # Adjust for fiscal years that span calendar years
                if (company.fiscal_year_end.month < company.fiscal_year_start.month) or \
                   (company.fiscal_year_end.month == company.fiscal_year_start.month and 
                    company.fiscal_year_end.day < company.fiscal_year_start.day):
                    # For comparative period, the end date should be in the same year as current period start
                    comparative_end = comparative_end.replace(year=year)
            elif period_type == 'calendar_year':
                comparative_start = date(year - 1, 1, 1)
                comparative_end = date(year - 1, 12, 31)
            else:  # custom - previous year same period
                comparative_start = period_start.replace(year=period_start.year - 1)
                comparative_end = period_end.replace(year=period_end.year - 1)
        
        # Get currency information
        if currency == 'functional':
            display_currency = company.functional_currency
        elif currency == 'reporting':
            display_currency = company.reporting_currency
        else:
            display_currency = currency
        
        # Build response data
        response_data = {
            'company_name': company.name,
            'period_type': period_type,
            'period_start': period_start.isoformat(),
            'period_end': period_end.isoformat(),
            'currency': display_currency,
            'include_comparatives': include_comparatives,
            'generated_at': timezone.now().isoformat(),
        }
        
        # Add comparative period info if requested
        if include_comparatives:
            response_data.update({
                'comparative_start': comparative_start.isoformat(),
                'comparative_end': comparative_end.isoformat(),
            })
        
        # Generate requested statements
        if statement_type in ['balance_sheet', 'all']:
            response_data['balance_sheet'] = generate_balance_sheet(
                period_end, 
                comparative_end if include_comparatives else None,
                period_type,
                period_start
            )
        
        if statement_type in ['income_statement', 'all']:
            response_data['income_statement'] = generate_income_statement(
                period_start, period_end, 
                comparative_start if include_comparatives else None,
                comparative_end if include_comparatives else None
            )
        
        if statement_type in ['cash_flow', 'all']:
            response_data['cash_flow'] = generate_cash_flow_statement(
                period_start, period_end,
                comparative_start if include_comparatives else None,
                comparative_end if include_comparatives else None
            )
        
        return Response(response_data)
        
    except Exception as e:
        return Response(
            {"detail": f"Error generating financial statements: {str(e)}"}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

def calculate_net_income_for_period(period_start, period_end):
    """Calculate Net Income for a specific period (excluding closing entries)"""
    from django.db.models import Sum, Case, When, Q, F, DecimalField
    
    # Get all income statement accounts (Revenue and Expenses)
    income_accounts = Account.objects.filter(
        account_type__financial_statement='INCOME_STATEMENT',
        is_active=True,
        is_header_account=False
    ).select_related('account_type')
    
    total_revenue = Decimal('0.00')
    total_expenses = Decimal('0.00')
    
    for account in income_accounts:
        # Calculate period totals for this account (excluding closing entries)
        period_debits = account.journal_lines.filter(
            journal_entry__transaction_date__gte=period_start,
            journal_entry__transaction_date__lte=period_end,
            journal_entry__status='POSTED'
        ).exclude(
            journal_entry__entry_number__startswith='CLOSE-'
        ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
        
        period_credits = account.journal_lines.filter(
            journal_entry__transaction_date__gte=period_start,
            journal_entry__transaction_date__lte=period_end,
            journal_entry__status='POSTED'
        ).exclude(
            journal_entry__entry_number__startswith='CLOSE-'
        ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
        
        if account.account_type.type == 'REVENUE':
            # Revenue accounts: Credits increase revenue
            total_revenue += (period_credits - period_debits)
        elif account.account_type.type == 'EXPENSE':
            # Expense accounts: Debits increase expenses
            total_expenses += (period_debits - period_credits)
    
    # Net Income = Revenue - Expenses
    return total_revenue - total_expenses

def generate_balance_sheet(as_of_date, comparative_date=None, period_type='fiscal_year', period_start=None):
    """Generate Professional Balance Sheet with Current vs Non-Current Classification"""
    from django.db.models import Sum, Case, When, Q, F, DecimalField
    from datetime import date
    from account.models import Company
    
    # Determine the period start for net income calculation
    if period_start:
        # Use the provided period start (from financial statements API)
        net_income_period_start = period_start
    elif period_type == 'calendar_year':
        # For calendar year, use January 1st of the current year
        current_year = as_of_date.year
        net_income_period_start = date(current_year, 1, 1)
    else:
        # For fiscal year or fallback, use company fiscal year settings
        company = Company.objects.first()
        if not company:
            # Fallback to calendar year if no company configured
            current_year = as_of_date.year
            net_income_period_start = date(current_year, 1, 1)
        else:
            # Use company's fiscal year settings
            current_year = as_of_date.year
            net_income_period_start = company.fiscal_year_start.replace(year=current_year)
            fiscal_year_end = company.fiscal_year_end.replace(year=current_year)
            
            # Adjust for fiscal years that span calendar years
            if (company.fiscal_year_end.month < company.fiscal_year_start.month) or \
               (company.fiscal_year_end.month == company.fiscal_year_start.month and 
                company.fiscal_year_end.day < company.fiscal_year_start.day):
                # If as_of_date is before the fiscal year end month, use previous year's fiscal start
                if as_of_date.month < company.fiscal_year_end.month or \
                   (as_of_date.month == company.fiscal_year_end.month and as_of_date.day <= company.fiscal_year_end.day):
                    net_income_period_start = company.fiscal_year_start.replace(year=current_year - 1)
                # Otherwise use current year's fiscal start
    
    # Calculate Net Income for the appropriate period (period start to as_of_date)
    current_year_net_income = calculate_net_income_for_period(net_income_period_start, as_of_date)
    
    # Get all balance sheet accounts (Assets, Liabilities, Equity)
    # Exclude temporary accounts like Income Summary (39999) from Balance Sheet
    balance_sheet_accounts = Account.objects.filter(
        account_type__financial_statement='BALANCE_SHEET',
        is_active=True,
        is_header_account=False
    ).exclude(
        account_number='39999'  # Exclude Income Summary - temporary closing account
    ).select_related('account_type', 'detail_type')
    
    # Calculate correct opening balance based on period type
    if period_type == 'calendar_year':
        # For calendar year, calculate opening balance as of January 1st of the year
        calendar_year_start = date(as_of_date.year, 1, 1)
        
        # Calculate balances as of the specified date with calendar year opening balance
        accounts_with_balances = balance_sheet_accounts.annotate(
            # Calculate opening balance as of January 1st (all transactions before calendar year start)
            opening_debits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__lt=calendar_year_start) &
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__debit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            opening_credits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__lt=calendar_year_start) &
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__credit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            # Calculate period transactions (from calendar year start to as_of_date)
            period_debits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__gte=calendar_year_start) &
                        Q(journal_lines__journal_entry__transaction_date__lte=as_of_date) &
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__debit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            period_credits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__gte=calendar_year_start) &
                        Q(journal_lines__journal_entry__transaction_date__lte=as_of_date) &
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__credit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            # Calculate final balance: opening_balance + all_historical_activity + period_activity
            balance=Case(
                When(
                    account_type__normal_balance='DEBIT',
                    then=F('opening_balance') + F('opening_debits') - F('opening_credits') + F('period_debits') - F('period_credits')
                ),
                default=F('opening_balance') + F('opening_credits') - F('opening_debits') + F('period_credits') - F('period_debits'),
                output_field=DecimalField(max_digits=15, decimal_places=2)
            )
        ).order_by('account_type__sort_order', 'account_number')
    else:
        # For fiscal year, use the original logic (opening balance + all transactions to date)
        accounts_with_balances = balance_sheet_accounts.annotate(
            total_debits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__lte=as_of_date) &
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__debit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            total_credits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__lte=as_of_date) &
                        Q(journal_lines__journal_entry__status='POSTED'),
                        then='journal_lines__credit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            balance=Case(
                When(
                    account_type__normal_balance='DEBIT',
                    then=F('opening_balance') + F('total_debits') - F('total_credits')
                ),
                default=F('opening_balance') + F('total_credits') - F('total_debits'),
                output_field=DecimalField(max_digits=15, decimal_places=2)
            )
        ).order_by('account_type__sort_order', 'account_number')
    
    # Calculate comparative balances if requested
    comparative_balances = {}
    if comparative_date:
        if period_type == 'calendar_year':
            # For calendar year comparative, calculate opening balance as of January 1st of comparative year
            comparative_calendar_start = date(comparative_date.year, 1, 1)
            
            comparative_accounts = balance_sheet_accounts.annotate(
                # Calculate opening balance as of January 1st of comparative year
                opening_debits=Sum(
                    Case(
                        When(
                            Q(journal_lines__journal_entry__transaction_date__lt=comparative_calendar_start) &
                            Q(journal_lines__journal_entry__status='POSTED'),
                            then='journal_lines__debit_amount'
                        ),
                        default=Decimal('0.00'),
                        output_field=DecimalField(max_digits=15, decimal_places=2)
                    )
                ),
                opening_credits=Sum(
                    Case(
                        When(
                            Q(journal_lines__journal_entry__transaction_date__lt=comparative_calendar_start) &
                            Q(journal_lines__journal_entry__status='POSTED'),
                            then='journal_lines__credit_amount'
                        ),
                        default=Decimal('0.00'),
                        output_field=DecimalField(max_digits=15, decimal_places=2)
                    )
                ),
                # Calculate period transactions for comparative year
                period_debits=Sum(
                    Case(
                        When(
                            Q(journal_lines__journal_entry__transaction_date__gte=comparative_calendar_start) &
                            Q(journal_lines__journal_entry__transaction_date__lte=comparative_date) &
                            Q(journal_lines__journal_entry__status='POSTED'),
                            then='journal_lines__debit_amount'
                        ),
                        default=Decimal('0.00'),
                        output_field=DecimalField(max_digits=15, decimal_places=2)
                    )
                ),
                period_credits=Sum(
                    Case(
                        When(
                            Q(journal_lines__journal_entry__transaction_date__gte=comparative_calendar_start) &
                            Q(journal_lines__journal_entry__transaction_date__lte=comparative_date) &
                            Q(journal_lines__journal_entry__status='POSTED'),
                            then='journal_lines__credit_amount'
                        ),
                        default=Decimal('0.00'),
                        output_field=DecimalField(max_digits=15, decimal_places=2)
                    )
                ),
                balance=Case(
                    When(
                        account_type__normal_balance='DEBIT',
                        then=F('opening_balance') + F('opening_debits') - F('opening_credits') + F('period_debits') - F('period_credits')
                    ),
                    default=F('opening_balance') + F('opening_credits') - F('opening_debits') + F('period_credits') - F('period_debits'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            )
        else:
            # For fiscal year comparative, use original logic
            comparative_accounts = balance_sheet_accounts.annotate(
                total_debits=Sum(
                    Case(
                        When(
                            Q(journal_lines__journal_entry__transaction_date__lte=comparative_date) &
                            Q(journal_lines__journal_entry__status='POSTED'),
                            then='journal_lines__debit_amount'
                        ),
                        default=Decimal('0.00'),
                        output_field=DecimalField(max_digits=15, decimal_places=2)
                    )
                ),
                total_credits=Sum(
                    Case(
                        When(
                            Q(journal_lines__journal_entry__transaction_date__lte=comparative_date) &
                            Q(journal_lines__journal_entry__status='POSTED'),
                            then='journal_lines__credit_amount'
                        ),
                        default=Decimal('0.00'),
                        output_field=DecimalField(max_digits=15, decimal_places=2)
                    )
                ),
                balance=Case(
                    When(
                        account_type__normal_balance='DEBIT',
                        then=F('opening_balance') + F('total_debits') - F('total_credits')
                    ),
                    default=F('opening_balance') + F('total_credits') - F('total_debits'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            )
            
            for account in comparative_accounts:
                comparative_balances[account.id] = float(account.balance or 0)
    
    # Professional Balance Sheet Structure with Current vs Non-Current
    current_assets = []
    non_current_assets = []
    current_liabilities = []
    non_current_liabilities = []
    equity = []
    
    total_current_assets = Decimal('0.00')
    total_non_current_assets = Decimal('0.00')
    total_current_liabilities = Decimal('0.00')
    total_non_current_liabilities = Decimal('0.00')
    total_equity = Decimal('0.00')
    
    for account in accounts_with_balances:
        balance = account.balance or Decimal('0.00')
        comparative_balance = comparative_balances.get(account.id, 0) if comparative_date else None
        
        account_data = {
            'account_id': account.id,
            'account_number': account.account_number,
            'account_name': account.account_name,
            'account_type': account.account_type.name,
            'detail_type': account.detail_type.name,
            'balance_sheet_classification': getattr(account.detail_type, 'balance_sheet_classification', ''),
            'balance': float(balance),
            'comparative_balance': comparative_balance,
            'variance': float(balance) - comparative_balance if comparative_balance is not None else None,
            'variance_percent': (
                ((float(balance) - comparative_balance) / abs(comparative_balance) * 100) 
                if comparative_balance and comparative_balance != 0 else None
            )
        }
        
        if account.account_type.type == 'ASSET':
            if getattr(account.detail_type, 'balance_sheet_classification', '') == 'CURRENT':
                current_assets.append(account_data)
                total_current_assets += balance
            else:
                non_current_assets.append(account_data)
                total_non_current_assets += balance
        elif account.account_type.type == 'LIABILITY':
            if getattr(account.detail_type, 'balance_sheet_classification', '') == 'CURRENT':
                current_liabilities.append(account_data)
                total_current_liabilities += balance
            else:
                non_current_liabilities.append(account_data)
                total_non_current_liabilities += balance
        elif account.account_type.type == 'EQUITY':
            # Keep all equity accounts as-is, no modifications
            equity.append(account_data)
            total_equity += balance
    
    # Add virtual "Profit for the year" line item (QuickBooks style)
    # This appears regardless of whether net income is zero or not
    virtual_profit_for_year = {
        'account_id': -1,  # Virtual account ID
        'account_number': 'VIRTUAL-PFY',
        'account_name': 'Profit for the year' if current_year_net_income >= 0 else 'Loss for the year',
        'account_type': 'Equity',
        'detail_type': 'Net Income',
        'balance_sheet_classification': '',
        'balance': float(current_year_net_income),
        'comparative_balance': None,
        'variance': None,
        'variance_percent': None,
        'is_virtual': True,
        'virtual_type': 'profit_for_year'
    }
    equity.append(virtual_profit_for_year)
    total_equity += current_year_net_income
    
    # Calculate totals for professional presentation
    total_assets = total_current_assets + total_non_current_assets
    total_liabilities = total_current_liabilities + total_non_current_liabilities
    
    return {
        'as_of_date': as_of_date.isoformat(),
        'comparative_date': comparative_date.isoformat() if comparative_date else None,
        # Professional Balance Sheet Structure
        'current_assets': current_assets,
        'non_current_assets': non_current_assets,
        'current_liabilities': current_liabilities,
        'non_current_liabilities': non_current_liabilities,
        'equity': equity,
        # Detailed totals
        'total_current_assets': float(total_current_assets),
        'total_non_current_assets': float(total_non_current_assets),
        'total_current_liabilities': float(total_current_liabilities),
        'total_non_current_liabilities': float(total_non_current_liabilities),
        'total_equity': float(total_equity),
        # Summary totals
        'total_assets': float(total_assets),
        'total_liabilities': float(total_liabilities),
        'total_liabilities_and_equity': float(total_liabilities + total_equity),
        'working_capital': float(total_current_assets - total_current_liabilities),
        'is_balanced': abs(total_assets - (total_liabilities + total_equity)) < Decimal('0.01'),
        # Net Income Information
        'current_year_net_income': float(current_year_net_income),
        'net_income_period_start': net_income_period_start.isoformat(),
        'net_income_period_type': period_type,
        # Legacy fields for backward compatibility
        'assets': current_assets + non_current_assets,
        'liabilities': current_liabilities + non_current_liabilities,
    }

def generate_income_statement(period_start, period_end, comparative_start=None, comparative_end=None):
    """Generate Income Statement data"""
    from django.db.models import Sum, Case, When, Q, F, DecimalField
    
    # Get all income statement accounts (Revenue, Expenses)
    income_accounts = Account.objects.filter(
        account_type__financial_statement='INCOME_STATEMENT',
        is_active=True,
        is_header_account=False
    ).select_related('account_type', 'detail_type')
    
    # Calculate period totals (excluding closing entries)
    accounts_with_totals = income_accounts.annotate(
        period_debits=Sum(
            Case(
                When(
                    Q(journal_lines__journal_entry__transaction_date__gte=period_start) &
                    Q(journal_lines__journal_entry__transaction_date__lte=period_end) &
                    Q(journal_lines__journal_entry__status='POSTED') &
                    ~Q(journal_lines__journal_entry__entry_number__startswith='CLOSE-'),
                    then='journal_lines__debit_amount'
                ),
                default=Decimal('0.00'),
                output_field=DecimalField(max_digits=15, decimal_places=2)
            )
        ),
        period_credits=Sum(
            Case(
                When(
                    Q(journal_lines__journal_entry__transaction_date__gte=period_start) &
                    Q(journal_lines__journal_entry__transaction_date__lte=period_end) &
                    Q(journal_lines__journal_entry__status='POSTED') &
                    ~Q(journal_lines__journal_entry__entry_number__startswith='CLOSE-'),
                    then='journal_lines__credit_amount'
                ),
                default=Decimal('0.00'),
                output_field=DecimalField(max_digits=15, decimal_places=2)
            )
        ),
        period_total=Case(
            When(
                account_type__normal_balance='CREDIT',  # Revenue
                then=F('period_credits') - F('period_debits')
            ),
            default=F('period_debits') - F('period_credits'),  # Expenses
            output_field=DecimalField(max_digits=15, decimal_places=2)
        )
    ).order_by('account_type__sort_order', 'account_number')
    
    # Calculate comparative period totals if requested (excluding closing entries)
    comparative_totals = {}
    if comparative_start and comparative_end:
        comparative_accounts = income_accounts.annotate(
            period_debits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__gte=comparative_start) &
                        Q(journal_lines__journal_entry__transaction_date__lte=comparative_end) &
                        Q(journal_lines__journal_entry__status='POSTED') &
                        ~Q(journal_lines__journal_entry__entry_number__startswith='CLOSE-'),
                        then='journal_lines__debit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            period_credits=Sum(
                Case(
                    When(
                        Q(journal_lines__journal_entry__transaction_date__gte=comparative_start) &
                        Q(journal_lines__journal_entry__transaction_date__lte=comparative_end) &
                        Q(journal_lines__journal_entry__status='POSTED') &
                        ~Q(journal_lines__journal_entry__entry_number__startswith='CLOSE-'),
                        then='journal_lines__credit_amount'
                    ),
                    default=Decimal('0.00'),
                    output_field=DecimalField(max_digits=15, decimal_places=2)
                )
            ),
            period_total=Case(
                When(
                    account_type__normal_balance='CREDIT',  # Revenue
                    then=F('period_credits') - F('period_debits')
                ),
                default=F('period_debits') - F('period_credits'),  # Expenses
                output_field=DecimalField(max_digits=15, decimal_places=2)
            )
        )
        
        for account in comparative_accounts:
            comparative_totals[account.id] = float(account.period_total or 0)
    
    # Organize by account type
    revenue = []
    expenses = []
    
    total_revenue = Decimal('0.00')
    total_expenses = Decimal('0.00')
    
    for account in accounts_with_totals:
        period_total = account.period_total or Decimal('0.00')
        comparative_total = comparative_totals.get(account.id, 0) if comparative_start else None
        
        account_data = {
            'account_id': account.id,
            'account_number': account.account_number,
            'account_name': account.account_name,
            'account_type': account.account_type.name,
            'period_total': float(period_total),
            'comparative_total': comparative_total,
            'variance': float(period_total) - comparative_total if comparative_total is not None else None,
            'variance_percent': (
                ((float(period_total) - comparative_total) / abs(comparative_total) * 100) 
                if comparative_total and comparative_total != 0 else None
            )
        }
        
        if account.account_type.type == 'REVENUE':
            revenue.append(account_data)
            total_revenue += period_total
        elif account.account_type.type == 'EXPENSE':
            expenses.append(account_data)
            total_expenses += period_total
    
    net_income = total_revenue - total_expenses
    
    return {
        'period_start': period_start.isoformat(),
        'period_end': period_end.isoformat(),
        'comparative_start': comparative_start.isoformat() if comparative_start else None,
        'comparative_end': comparative_end.isoformat() if comparative_end else None,
        'revenue': revenue,
        'expenses': expenses,
        'total_revenue': float(total_revenue),
        'total_expenses': float(total_expenses),
        'net_income': float(net_income),
        'gross_profit_margin': (float(net_income) / float(total_revenue) * 100) if total_revenue > 0 else 0
    }

def generate_cash_flow_statement(period_start, period_end, comparative_start=None, comparative_end=None):
    """Generate Cash Flow Statement data (simplified version)"""
    # This is a simplified cash flow statement
    # In a full implementation, you would categorize cash flows into:
    # - Operating Activities
    # - Investing Activities  
    # - Financing Activities
    
    from django.db.models import Sum, Case, When, Q
    
    # Get cash and cash equivalent accounts
    cash_accounts = Account.objects.filter(
        Q(account_name__icontains='cash') | Q(account_name__icontains='bank'),
        account_type__type='ASSET',
        is_active=True,
        is_header_account=False
    ).select_related('account_type')
    
    cash_flows = []
    total_cash_flow = Decimal('0.00')
    
    for account in cash_accounts:
        # Calculate net cash flow for the period
        period_debits = account.journal_lines.filter(
            journal_entry__transaction_date__gte=period_start,
            journal_entry__transaction_date__lte=period_end,
            journal_entry__status='POSTED'
        ).aggregate(total=Sum('debit_amount'))['total'] or Decimal('0.00')
        
        period_credits = account.journal_lines.filter(
            journal_entry__transaction_date__gte=period_start,
            journal_entry__transaction_date__lte=period_end,
            journal_entry__status='POSTED'
        ).aggregate(total=Sum('credit_amount'))['total'] or Decimal('0.00')
        
        net_flow = period_debits - period_credits
        total_cash_flow += net_flow
        
        cash_flows.append({
            'account_id': account.id,
            'account_number': account.account_number,
            'account_name': account.account_name,
            'cash_inflows': float(period_debits),
            'cash_outflows': float(period_credits),
            'net_cash_flow': float(net_flow)
        })
    
    return {
        'period_start': period_start.isoformat(),
        'period_end': period_end.isoformat(),
        'cash_accounts': cash_flows,
        'total_cash_flow': float(total_cash_flow),
        'note': 'This is a simplified cash flow statement. Full implementation would categorize by operating, investing, and financing activities.'
    }

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_account_types(request):
    """Get all active account types."""
    account_types = AccountType.objects.filter(is_active=True).order_by('sort_order')
    data = []
    for account_type in account_types:
        data.append({
            'id': account_type.id,
            'code': account_type.code,
            'name': account_type.name,
            'type': account_type.type,
            'normal_balance': account_type.normal_balance,
            'financial_statement': account_type.financial_statement,
        })
    return Response(data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_detail_types(request):
    """Get detail types, optionally filtered by account type."""
    account_type_id = request.GET.get('account_type_id')
    
    detail_types = DetailType.objects.filter(is_active=True)
    if account_type_id:
        detail_types = detail_types.filter(account_type_id=account_type_id)
    
    detail_types = detail_types.order_by('account_type__sort_order', 'sort_order')
    
    data = []
    for detail_type in detail_types:
        data.append({
            'id': detail_type.id,
            'code': detail_type.code,
            'name': detail_type.name,
            'account_type_id': detail_type.account_type_id,
            'account_type_name': detail_type.account_type.name,
            'description': detail_type.description,
        })
    return Response(data)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_accounts(request):
    """Get accounts with optional filtering."""
    accounts = Account.objects.filter(is_active=True).select_related(
        'account_type', 'detail_type', 'parent_account'
    ).order_by('account_number')
    
    # Apply filters
    account_type = request.GET.get('account_type')
    if account_type:
        accounts = accounts.filter(account_type__code=account_type)
    
    detail_type = request.GET.get('detail_type')
    if detail_type:
        accounts = accounts.filter(detail_type__code=detail_type)
    
    # Serialize data
    serializer = AccountSerializer(accounts, many=True)
    return Response(serializer.data)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def upload_coa(request):
    """Upload Chart of Accounts from CSV file."""
    
    if 'file' not in request.FILES:
        return Response(
            {'error': 'No file provided'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    csv_file = request.FILES['file']
    
    # Validate file type
    if not csv_file.name.endswith('.csv'):
        return Response(
            {'error': 'File must be a CSV file'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    # Validate file size (max 5MB)
    if csv_file.size > 5 * 1024 * 1024:
        return Response(
            {'error': 'File size too large (max 5MB)'}, 
            status=status.HTTP_400_BAD_REQUEST
        )
    
    dry_run = request.data.get('dry_run', 'false').lower() == 'true'
    
    try:
        # Process the uploaded file
        accounts_created, errors, warnings = process_coa_csv(csv_file, request.user, dry_run)
        
        response_data = {
            'success': len(errors) == 0,
            'accounts_processed': accounts_created,
            'errors': errors,
            'warnings': warnings,
            'dry_run': dry_run
        }
        
        if errors:
            return Response(response_data, status=status.HTTP_400_BAD_REQUEST)
        
        return Response(response_data, status=status.HTTP_200_OK)
        
    except Exception as e:
        return Response(
            {'error': f'Upload failed: {str(e)}'}, 
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_coa_template(request):
    """Get COA upload template information."""
    
    # Get available account types and detail types
    account_types = AccountType.objects.filter(is_active=True).order_by('sort_order')
    detail_types = DetailType.objects.filter(is_active=True).order_by('account_type__sort_order', 'sort_order')
    
    template_info = {
        'required_fields': [
            'account_number',
            'account_name', 
            'account_type_code',
            'detail_type_code'
        ],
        'optional_fields': [
            'parent_account_number',
            'description',
            'opening_balance',
            'opening_balance_date',
            'currency',
            'is_active',
            'is_header_account',
            'tax_line',
            'bank_account_number',
            'bank_routing_number'
        ],
        'account_types': [
            {
                'code': at.code,
                'name': at.name,
                'type': at.type,
                'normal_balance': at.normal_balance
            }
            for at in account_types
        ],
        'detail_types': [
            {
                'code': dt.code,
                'name': dt.name,
                'account_type_code': dt.account_type.code,
                'description': dt.description
            }
            for dt in detail_types
        ],
        'format_rules': {
            'account_number': 'Unique identifier (max 20 chars)',
            'account_name': 'Descriptive name (max 200 chars)',
            'account_type_code': 'Must match exactly: ASSET, LIABILITY, EQUITY, REVENUE, EXPENSE',
            'detail_type_code': 'Must exist for the specified account type',
            'opening_balance': 'Decimal format (e.g., 1000.00)',
            'opening_balance_date': 'Date format: YYYY-MM-DD',
            'currency': 'ISO currency code (e.g., INR, USD)',
            'is_active': 'Boolean: TRUE/FALSE or 1/0',
            'is_header_account': 'Boolean: TRUE/FALSE or 1/0'
        }
    }
    
    return Response(template_info)

def process_coa_csv(csv_file, user, dry_run=False):
    """Process COA CSV file and return results."""
    accounts_created = 0
    errors = []
    warnings = []
    
    # Required fields
    required_fields = ['account_number', 'account_name', 'account_type_code', 'detail_type_code']
    
    # Cache for lookups
    account_types_cache = {}
    detail_types_cache = {}
    account_numbers_seen = set()
    
    try:
        # Decode file content
        file_content = csv_file.read().decode('utf-8')
        csv_reader = csv.DictReader(io.StringIO(file_content))
        
        # Validate headers
        if not all(field in csv_reader.fieldnames for field in required_fields):
            missing_fields = [field for field in required_fields if field not in csv_reader.fieldnames]
            errors.append(f'Missing required columns: {", ".join(missing_fields)}')
            return 0, errors, warnings
        
        with transaction.atomic():
            for row_num, row in enumerate(csv_reader, start=2):
                try:
                    # Validate required fields
                    missing_required = []
                    for field in required_fields:
                        if not row.get(field, '').strip():
                            missing_required.append(field)
                    
                    if missing_required:
                        errors.append(f'Row {row_num}: Missing required fields: {", ".join(missing_required)}')
                        continue
                    
                    account_number = row['account_number'].strip()
                    account_name = row['account_name'].strip()
                    account_type_code = row['account_type_code'].strip().upper()
                    detail_type_code = row['detail_type_code'].strip().upper()
                    
                    # Check for duplicate account numbers in file
                    if account_number in account_numbers_seen:
                        errors.append(f'Row {row_num}: Duplicate account number "{account_number}" in file')
                        continue
                    
                    # Check if account already exists in database
                    if Account.objects.filter(account_number=account_number).exists():
                        errors.append(f'Row {row_num}: Account number "{account_number}" already exists in database')
                        continue
                    
                    account_numbers_seen.add(account_number)
                    
                    # Validate account type
                    if account_type_code not in account_types_cache:
                        try:
                            account_type = AccountType.objects.get(code=account_type_code)
                            account_types_cache[account_type_code] = account_type
                        except AccountType.DoesNotExist:
                            errors.append(f'Row {row_num}: Invalid account type code "{account_type_code}"')
                            continue
                    
                    account_type = account_types_cache[account_type_code]
                    
                    # Validate detail type
                    detail_type_key = f'{account_type_code}_{detail_type_code}'
                    if detail_type_key not in detail_types_cache:
                        try:
                            detail_type = DetailType.objects.get(
                                account_type=account_type,
                                code=detail_type_code
                            )
                            detail_types_cache[detail_type_key] = detail_type
                        except DetailType.DoesNotExist:
                            errors.append(
                                f'Row {row_num}: Invalid detail type code "{detail_type_code}" '
                                f'for account type "{account_type_code}"'
                            )
                            continue
                    
                    detail_type = detail_types_cache[detail_type_key]
                    
                    # Process optional fields
                    parent_account = None
                    if row.get('parent_account_number', '').strip():
                        parent_account_number = row['parent_account_number'].strip()
                        try:
                            parent_account = Account.objects.get(account_number=parent_account_number)
                        except Account.DoesNotExist:
                            errors.append(f'Row {row_num}: Parent account "{parent_account_number}" not found')
                            continue
                    
                    # Parse opening balance
                    opening_balance = Decimal('0.00')
                    if row.get('opening_balance', '').strip():
                        try:
                            opening_balance = Decimal(row['opening_balance'].strip())
                        except (InvalidOperation, ValueError):
                            errors.append(f'Row {row_num}: Invalid opening balance format')
                            continue
                    
                    # Parse opening balance date
                    opening_balance_date = None
                    if row.get('opening_balance_date', '').strip():
                        try:
                            opening_balance_date = datetime.strptime(
                                row['opening_balance_date'].strip(), '%Y-%m-%d'
                            ).date()
                        except ValueError:
                            errors.append(f'Row {row_num}: Invalid date format (use YYYY-MM-DD)')
                            continue
                    
                    # Parse other fields
                    description = row.get('description', '').strip()
                    currency = row.get('currency', 'INR').strip().upper()
                    is_active = parse_boolean(row.get('is_active', 'TRUE'))
                    is_header_account = parse_boolean(row.get('is_header_account', 'FALSE'))
                    tax_line = row.get('tax_line', '').strip()
                    bank_account_number = row.get('bank_account_number', '').strip()
                    bank_routing_number = row.get('bank_routing_number', '').strip()
                    
                    # Create account if not dry run
                    if not dry_run:
                        Account.objects.create(
                            account_number=account_number,
                            account_name=account_name,
                            account_type=account_type,
                            detail_type=detail_type,
                            parent_account=parent_account,
                            description=description,
                            opening_balance=opening_balance,
                            opening_balance_date=opening_balance_date,
                            currency=currency,
                            is_active=is_active,
                            is_header_account=is_header_account,
                            tax_line=tax_line,
                            bank_account_number=bank_account_number,
                            bank_routing_number=bank_routing_number,
                            created_by=user
                        )
                    
                    accounts_created += 1
                    
                except Exception as e:
                    errors.append(f'Row {row_num}: Unexpected error - {str(e)}')
            
            # Rollback if dry run or if there are errors
            if dry_run or errors:
                transaction.set_rollback(True)
    
    except Exception as e:
        errors.append(f'File processing error: {str(e)}')
    
    return accounts_created, errors, warnings

def parse_boolean(value):
    """Parse boolean values from CSV."""
    if isinstance(value, bool):
        return value
    
    if isinstance(value, str):
        value = value.strip().upper()
        return value in ['TRUE', '1', 'YES', 'Y']
    
    return bool(value)


# Odoo-style ViewSets

class AccountMoveViewSet(viewsets.ModelViewSet):
    """
    Odoo-style AccountMove ViewSet
    Handles all accounting moves (invoices, bills, journal entries, payments)
    """
    queryset = AccountMove.objects.all().order_by('-date', '-id')
    serializer_class = AccountMoveSerializer
    permission_classes = []  # Temporarily disable auth for testing
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['move_type', 'state', 'partner_id', 'journal_id']
    search_fields = ['name', 'ref', 'partner_name']
    ordering_fields = ['date', 'name', 'amount_total']
    ordering = ['-date', '-id']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Only add date filtering if request has query_params
        if hasattr(self.request, 'query_params'):
            # Filter by date range
            date_from = self.request.query_params.get('date_from')
            date_to = self.request.query_params.get('date_to')

            if date_from:
                queryset = queryset.filter(date__gte=date_from)
            if date_to:
                queryset = queryset.filter(date__lte=date_to)

        return queryset

    def perform_create(self, serializer):
        """Set created_uid when creating new move"""
        # Debug logging
        print("=== AccountMove CREATE DEBUG ===")
        print(f"Request data: {self.request.data}")
        print(f"Validated data: {serializer.validated_data}")
        print("=== END DEBUG ===")

        serializer.save(created_uid=self.request.user)

    def perform_update(self, serializer):
        """Set write_uid when updating move"""
        serializer.save(write_uid=self.request.user)

    @action(detail=True, methods=['post'])
    def action_post(self, request, pk=None):
        """
        Odoo-style action_post method
        Posts the account move (draft -> posted)
        """
        move = self.get_object()

        if move.state != 'draft':
            return Response(
                {'error': 'Only draft moves can be posted'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            move.action_post()
            return Response({'message': 'Move posted successfully'})
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def button_cancel(self, request, pk=None):
        """
        Odoo-style button_cancel method
        Cancels the account move
        """
        move = self.get_object()

        if move.state == 'cancel':
            return Response(
                {'error': 'Move is already cancelled'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            move.state = 'cancel'
            move.save()
            return Response({'message': 'Move cancelled successfully'})
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def button_draft(self, request, pk=None):
        """
        Odoo-style button_draft method
        Resets move to draft state
        """
        move = self.get_object()

        if move.state == 'draft':
            return Response(
                {'error': 'Move is already in draft state'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            move.state = 'draft'
            move.save()
            return Response({'message': 'Move reset to draft successfully'})
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=True, methods=['post'])
    def copy(self, request, pk=None):
        """
        Odoo-style copy method
        Duplicates the account move
        """
        original_move = self.get_object()

        try:
            # Create a copy of the move
            new_move = AccountMove.objects.create(
                name='/',  # Will be set by sequence
                move_type=original_move.move_type,
                state='draft',
                ref=f"Copy of {original_move.ref}" if original_move.ref else '',
                date=timezone.now().date(),
                partner_id=original_move.partner_id,
                journal_id=original_move.journal_id,
                created_uid=request.user
            )

            # Copy move lines
            for line in original_move.line_ids.all():
                AccountMoveLine.objects.create(
                    move_id=new_move,
                    account_id=line.account_id,
                    partner_id=line.partner_id,
                    name=line.name,
                    debit=line.debit,
                    credit=line.credit,
                    product_id=line.product_id,
                    quantity=line.quantity,
                    price_unit=line.price_unit,
                )

            return Response({
                'message': 'Move duplicated successfully',
                'new_move_id': new_move.id
            })
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """
        Get account move statistics
        """
        try:
            queryset = self.get_queryset()

            stats = {
                'total_moves': queryset.count(),
                'draft_moves': queryset.filter(state='draft').count(),
                'posted_moves': queryset.filter(state='posted').count(),
                'total_amount': float(queryset.aggregate(
                    total=Sum('amount_total')
                )['total'] or 0),
                'by_type': {}
            }

            # Stats by move type
            for move_type, label in AccountMove.MOVE_TYPE_CHOICES:
                type_queryset = queryset.filter(move_type=move_type)
                stats['by_type'][move_type] = {
                    'count': type_queryset.count(),
                    'amount': float(type_queryset.aggregate(
                        total=Sum('amount_total')
                    )['total'] or 0)
                }

            return Response(stats)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class AccountJournalViewSet(viewsets.ModelViewSet):
    """
    Odoo-style AccountJournal ViewSet
    Manages journals for categorizing transactions
    """
    queryset = AccountJournal.objects.filter(active=True).order_by('code')
    serializer_class = AccountJournalSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['type', 'active']
    search_fields = ['name', 'code']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Show inactive journals to admins only
        if not self.request.user.is_staff:
            queryset = queryset.filter(active=True)

        return queryset


class IrSequenceViewSet(viewsets.ModelViewSet):
    """
    Odoo-style IrSequence ViewSet
    Manages document numbering sequences
    """
    queryset = IrSequence.objects.filter(active=True).order_by('name')
    serializer_class = IrSequenceSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter]
    filterset_fields = ['active', 'use_date_range']
    search_fields = ['name', 'code']

    def get_queryset(self):
        queryset = super().get_queryset()

        # Show inactive sequences to admins only
        if not self.request.user.is_staff:
            queryset = queryset.filter(active=True)

        return queryset

    @action(detail=True, methods=['post'])
    def next_number(self, request, pk=None):
        """
        Get the next number from this sequence
        """
        sequence = self.get_object()

        try:
            next_num = sequence.next_by_id()
            return Response({'next_number': next_num})
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_400_BAD_REQUEST
            )