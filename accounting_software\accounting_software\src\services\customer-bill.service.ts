import api from './api';

// Customer Bill Types
export interface CustomerBillCustomer {
  id: number;
  display_name: string;
  email?: string;
  phone?: string;
  billing_address?: string;
  payment_terms?: string;
}

export interface CustomerBillItem {
  id?: number;
  product?: number;
  product_name?: string;
  item_description: string;
  quantity: number;
  unit_price: number;
  line_total: number;
  taxable?: boolean;
  tax_rate?: number;
  tax_amount?: number;
  account_code?: string;
  line_order?: number;
}

export interface CustomerBill {
  id: number;
  bill_number: string;
  customer: number;
  customer_details?: CustomerBillCustomer;
  customer_name?: string; // Computed field for display
  bill_date: string;
  due_date: string;
  status: 'draft' | 'posted' | 'paid';
  bill_type?: 'bill' | 'credit';
  line_items: CustomerBillItem[];
  subtotal: number;
  tax_amount: number;
  total_amount: number;
  amount_paid: number;
  balance_due: number;
  notes?: string;
  payment_terms?: string;
  reference_number?: string;
  source_type?: 'manual' | 'sales_order' | 'delivery_note' | 'return_note';
  sales_order?: number;
  delivery_note?: number;
  delivery_return_note?: number;
  created_at?: string;
  updated_at?: string;
}

export interface CustomerBillFilters {
  status?: string;
  customer?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface CustomerBillStats {
  total_bills: number;
  total_receivables: number;
  outstanding_amount: number;
  overdue_count: number;
  draft_count: number;
  paid_count: number;
}

class CustomerBillService {
  private baseUrl = '/api/sales';

  // Get customer bills with filters and pagination
  async getCustomerBills(filters?: CustomerBillFilters): Promise<{ results: CustomerBill[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters?.status) params.append('status', filters.status);
    if (filters?.customer) params.append('customer', filters.customer.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`${this.baseUrl}/customer-bills/?${params.toString()}`);

    // Map the response to include customer_name for display
    const mappedResults = response.data.results?.map((bill: any) => ({
      ...bill,
      customer_name: bill.customer_details?.display_name || bill.customer_details?.name || 'Unknown Customer'
    })) || [];

    return {
      results: mappedResults,
      count: response.data.count || 0
    };
  }

  // Get single customer bill by ID
  async getCustomerBill(id: number): Promise<CustomerBill> {
    const response = await api.get(`${this.baseUrl}/customer-bills/${id}/`);
    return response.data;
  }

  // Create new customer bill
  async createCustomerBill(billData: Omit<CustomerBill, 'id' | 'bill_number' | 'created_at' | 'updated_at'>): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/`, billData);
    return response.data;
  }

  // Update existing customer bill
  async updateCustomerBill(id: number, billData: Partial<CustomerBill>): Promise<CustomerBill> {
    const response = await api.patch(`${this.baseUrl}/customer-bills/${id}/`, billData);
    return response.data;
  }

  // Delete customer bill
  async deleteCustomerBill(id: number): Promise<void> {
    await api.delete(`${this.baseUrl}/customer-bills/${id}/`);
  }

  // Get customer bill statistics
  async getCustomerBillStats(): Promise<CustomerBillStats> {
    const response = await api.get(`${this.baseUrl}/customer-bills/stats/`);
    const backendStats = response.data;

    // Map backend response to frontend interface
    return {
      total_bills: backendStats.total_bills || 0,
      total_receivables: backendStats.total_receivables || 0,
      outstanding_amount: backendStats.outstanding_amount || 0,
      overdue_count: 0, // Backend doesn't provide this yet
      draft_count: backendStats.draft_bills || 0,
      paid_count: backendStats.paid_bills || 0,
    };
  }

  // Post customer bill to create GL entries
  async postCustomerBill(id: number): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/${id}/post/`);
    return response.data;
  }

  // Mark customer bill as paid
  async markAsPaid(id: number, paymentData: { payment_date: string; payment_amount: number; payment_method?: string; notes?: string }): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/${id}/mark_paid/`, paymentData);
    return response.data;
  }

  // Send customer bill (email)
  async sendCustomerBill(id: number, emailData?: { to_email?: string; subject?: string; message?: string }): Promise<void> {
    await api.post(`${this.baseUrl}/customer-bills/${id}/send/`, emailData || {});
  }

  // Duplicate customer bill
  async duplicateCustomerBill(id: number): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/${id}/duplicate/`);
    return response.data;
  }

  // Get customer bills for a specific customer
  async getCustomerBillsByCustomer(customerId: number): Promise<CustomerBill[]> {
    const response = await api.get(`${this.baseUrl}/customer-bills/?customer=${customerId}`);
    return response.data.results || [];
  }

  // Get overdue customer bills
  async getOverdueCustomerBills(): Promise<CustomerBill[]> {
    const response = await api.get(`${this.baseUrl}/customer-bills/overdue/`);
    return response.data.results || [];
  }

  // Get customer bills summary for dashboard
  async getCustomerBillsSummary(): Promise<{
    total_bills: number;
    total_amount: number;
    paid_amount: number;
    outstanding_amount: number;
    overdue_amount: number;
  }> {
    const response = await api.get(`${this.baseUrl}/customer-bills/summary/`);
    return response.data;
  }

  // Create customer bill from Sales Order
  async createCustomerBillFromSO(soId: number): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/create_from_sales_order/`, {
      sales_order_id: soId
    });

    if (response.status !== 201) {
      throw new Error('Failed to create customer bill from Sales Order');
    }

    return response.data;
  }

  // Create customer bill from Delivery Note
  async createCustomerBillFromDN(dnId: number, billData?: { bill_date: string; due_date: string; notes?: string }): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/create_from_delivery_note/`, {
      delivery_note_id: dnId,
      ...billData
    });

    if (response.status !== 201) {
      throw new Error('Failed to create customer bill from Delivery Note');
    }

    return response.data;
  }

  // Create customer bill from Return Note
  async createCustomerBillFromReturnNote(returnNoteId: number): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/create_from_return_note/`, {
      return_note_id: returnNoteId
    });

    if (response.status !== 201) {
      throw new Error('Failed to create customer bill from Return Note');
    }

    return response.data;
  }

  // Get billable Sales Orders
  async getBillableSalesOrders(): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/customer-bills/billable_sales_orders/`);
    return response.data;
  }

  // Create customer credit bill from Delivery Return Note
  async createCustomerCreditFromReturnNote(returnNoteId: number, billData?: { bill_date: string; due_date: string; notes?: string }): Promise<CustomerBill> {
    const response = await api.post(`${this.baseUrl}/customer-bills/create_credit_from_return_note/`, {
      return_note_id: returnNoteId,
      ...billData
    });

    if (response.status !== 201) {
      throw new Error('Failed to create customer credit from Return Note');
    }

    return response.data;
  }

  // Get billable Delivery Notes
  async getBillableDeliveryNotes(): Promise<any[]> {
    const response = await api.get(`${this.baseUrl}/customer-bills/billable_delivery_notes/`);
    return response.data;
  }

  // Generate new bill number
  async generateBillNumber(): Promise<string> {
    const response = await api.get(`${this.baseUrl}/customer-bills/generate_bill_number/`);
    return response.data.bill_number;
  }
}

export const customerBillService = new CustomerBillService();
