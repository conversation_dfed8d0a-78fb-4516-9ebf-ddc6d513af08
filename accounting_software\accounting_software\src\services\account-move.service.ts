/**
 * Odoo-style Account Move Service
 * Handles all accounting moves (invoices, bills, journal entries, payments)
 */

import api from './api';
import { AccountMove, AccountMoveLine, AccountJournal, IrSequence } from '../shared/types/gl.types';

export interface AccountMoveFilters {
  move_type?: string;
  state?: string;
  partner_id?: number;
  journal_id?: number;
  date_from?: string;
  date_to?: string;
  search?: string;
}

export interface AccountMoveStats {
  total_moves: number;
  draft_moves: number;
  posted_moves: number;
  total_amount: number;
  by_type: {
    [key: string]: {
      count: number;
      amount: number;
    };
  };
}

class AccountMoveService {
  private baseUrl = '/gl';

  // Get account moves with filters
  async getAccountMoves(filters?: AccountMoveFilters): Promise<{ results: AccountMove[]; count: number }> {
    const params = new URLSearchParams();
    
    if (filters?.move_type) params.append('move_type', filters.move_type);
    if (filters?.state) params.append('state', filters.state);
    if (filters?.partner_id) params.append('partner_id', filters.partner_id.toString());
    if (filters?.journal_id) params.append('journal_id', filters.journal_id.toString());
    if (filters?.date_from) params.append('date_from', filters.date_from);
    if (filters?.date_to) params.append('date_to', filters.date_to);
    if (filters?.search) params.append('search', filters.search);

    const response = await api.get(`${this.baseUrl}/account-moves/?${params.toString()}`);
    return {
      results: response.data.results || [],
      count: response.data.count || 0
    };
  }

  // Get single account move
  async getAccountMove(id: number): Promise<AccountMove> {
    const response = await api.get(`${this.baseUrl}/account-moves/${id}/`);
    return response.data;
  }

  // Create new account move
  async createAccountMove(moveData: Partial<AccountMove>): Promise<AccountMove> {
    const response = await api.post(`${this.baseUrl}/account-moves/`, moveData);
    return response.data;
  }

  // Update account move
  async updateAccountMove(id: number, moveData: Partial<AccountMove>): Promise<AccountMove> {
    const response = await api.patch(`${this.baseUrl}/account-moves/${id}/`, moveData);
    return response.data;
  }

  // Post account move (Odoo action_post equivalent)
  async postAccountMove(id: number): Promise<AccountMove> {
    const response = await api.post(`${this.baseUrl}/account-moves/${id}/action_post/`);
    return response.data;
  }

  // Cancel account move
  async cancelAccountMove(id: number): Promise<AccountMove> {
    const response = await api.post(`${this.baseUrl}/account-moves/${id}/button_cancel/`);
    return response.data;
  }

  // Reset to draft
  async resetToDraft(id: number): Promise<AccountMove> {
    const response = await api.post(`${this.baseUrl}/account-moves/${id}/button_draft/`);
    return response.data;
  }

  // Get account move statistics
  async getAccountMoveStats(): Promise<AccountMoveStats> {
    const response = await api.get(`${this.baseUrl}/account-moves/stats/`);
    return response.data;
  }

  // Get journals
  async getJournals(): Promise<AccountJournal[]> {
    const response = await api.get(`${this.baseUrl}/journals/`);
    return response.data.results || response.data;
  }

  // Get sequences
  async getSequences(): Promise<IrSequence[]> {
    const response = await api.get(`${this.baseUrl}/sequences/`);
    return response.data.results || response.data;
  }

  // Create journal entry (convenience method)
  async createJournalEntry(entryData: {
    ref?: string;
    date: string;
    journal_id: number;
    line_ids: Partial<AccountMoveLine>[];
  }): Promise<AccountMove> {
    const moveData: Partial<AccountMove> = {
      move_type: 'entry',
      state: 'draft',
      ref: entryData.ref,
      date: entryData.date,
      journal_id: entryData.journal_id,
      line_ids: entryData.line_ids
    };

    return this.createAccountMove(moveData);
  }

  // Create customer invoice (convenience method)
  async createCustomerInvoice(invoiceData: {
    partner_id: number;
    date: string;
    ref?: string;
    line_ids: Partial<AccountMoveLine>[];
  }): Promise<AccountMove> {
    const moveData: Partial<AccountMove> = {
      move_type: 'out_invoice',
      state: 'draft',
      partner_id: invoiceData.partner_id,
      date: invoiceData.date,
      ref: invoiceData.ref,
      line_ids: invoiceData.line_ids
    };

    return this.createAccountMove(moveData);
  }

  // Create vendor bill (convenience method)
  async createVendorBill(billData: {
    partner_id: number;
    date: string;
    ref?: string;
    line_ids: Partial<AccountMoveLine>[];
  }): Promise<AccountMove> {
    const moveData: Partial<AccountMove> = {
      move_type: 'in_invoice',
      state: 'draft',
      partner_id: billData.partner_id,
      date: billData.date,
      ref: billData.ref,
      line_ids: billData.line_ids
    };

    return this.createAccountMove(moveData);
  }

  // Duplicate account move
  async duplicateAccountMove(id: number): Promise<AccountMove> {
    const response = await api.post(`${this.baseUrl}/account-moves/${id}/copy/`);
    return response.data;
  }

  // Get move types for filtering
  getMoveTypes(): Array<{ value: string; label: string }> {
    return [
      { value: 'entry', label: 'Journal Entry' },
      { value: 'out_invoice', label: 'Customer Invoice' },
      { value: 'out_refund', label: 'Customer Credit Note' },
      { value: 'in_invoice', label: 'Vendor Bill' },
      { value: 'in_refund', label: 'Vendor Credit Note' },
      { value: 'out_receipt', label: 'Sales Receipt' },
      { value: 'in_receipt', label: 'Purchase Receipt' },
    ];
  }

  // Get state options
  getStateOptions(): Array<{ value: string; label: string }> {
    return [
      { value: 'draft', label: 'Draft' },
      { value: 'posted', label: 'Posted' },
      { value: 'cancel', label: 'Cancelled' },
    ];
  }
}

export const accountMoveService = new AccountMoveService();
export default accountMoveService;
