"""
General Ledger Serializers for ERP Accounting Software

These serializers handle the conversion between Django models and JSON API responses
for all General Ledger functionality. They include comprehensive business logic
validation to ensure accounting integrity and compliance with IFRS standards.

Key Features:
- Chart of Accounts management with hierarchical structure
- Journal entry creation and posting with double-entry validation
- Account type and detail type management like QuickBooks
- Comprehensive error handling and validation messages
- Support for multi-currency transactions
"""

from rest_framework import serializers
from decimal import Decimal
from django.db import transaction
from django.utils import timezone
from .models import (
    AccountType, DetailType, Account, JournalEntry, JournalEntryLine,
    Project, Product, VoucherType, RecurringJournalEntry, RecurringJournalEntryLine,
    AccountMove, AccountMoveLine, AccountJournal, IrSequence
)
from sales_tax.models import SalesTax
from tds.models import TDS

class AccountTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for Account Types (Assets, Liabilities, Equity, Revenue, Expenses)
    
    Account types are the fundamental categories in accounting that determine:
    - How accounts behave in terms of debits and credits
    - Which financial statement they appear on
    - Their normal balance (debit or credit)
    
    This serializer ensures proper account type setup for IFRS compliance.
    """
    
    # Add count of accounts using this type
    accounts_count = serializers.SerializerMethodField()
    
    class Meta:
        model = AccountType
        fields = [
            'id', 'code', 'name', 'type', 'normal_balance', 
            'financial_statement', 'sort_order', 'is_active',
            'accounts_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'accounts_count']
    
    def get_accounts_count(self, obj):
        """Get the number of accounts using this account type."""
        return obj.accounts.filter(is_active=True).count()
    
    def validate_code(self, value):
        """Ensure account type code is uppercase and unique."""
        value = value.upper().strip()
        if not value.isalnum():
            raise serializers.ValidationError(
                "Account type code must contain only letters and numbers"
            )
        return value
    
    def validate(self, data):
        """Cross-field validation for account type setup."""
        
        # Ensure normal balance matches account type
        type_balance_map = {
            'ASSET': 'DEBIT',
            'EXPENSE': 'DEBIT',
            'LIABILITY': 'CREDIT',
            'EQUITY': 'CREDIT',
            'REVENUE': 'CREDIT',
        }
        
        if data.get('type') and data.get('normal_balance'):
            expected_balance = type_balance_map.get(data['type'])
            if expected_balance and data['normal_balance'] != expected_balance:
                raise serializers.ValidationError({
                    'normal_balance': f"Account type '{data['type']}' should have '{expected_balance}' normal balance"
                })
        
        return data

class DetailTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for Detail Types (QuickBooks-style account classifications)
    
    Detail types provide more specific categorization within account types:
    - Cash, Bank, Accounts Receivable for Assets
    - Advertising, Office Supplies, Travel for Expenses
    - Short-term Debt, Accounts Payable for Liabilities
    
    This allows for better financial reporting and automated account behavior.
    """
    
    # Include parent account type information
    account_type_name = serializers.CharField(source='account_type.name', read_only=True)
    account_type_code = serializers.CharField(source='account_type.code', read_only=True)
    
    # Count of accounts using this detail type
    accounts_count = serializers.SerializerMethodField()
    
    class Meta:
        model = DetailType
        fields = [
            'id', 'code', 'name', 'description', 'account_type', 
            'account_type_name', 'account_type_code',
            'requires_customer', 'requires_vendor', 'requires_item',
            'is_tax_account', 'default_tax_code', 'balance_sheet_classification',
            'sort_order', 'is_active', 'accounts_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'accounts_count']
    
    def get_accounts_count(self, obj):
        """Get the number of accounts using this detail type."""
        return obj.accounts.filter(is_active=True).count()
    
    def validate_code(self, value):
        """Ensure detail type code follows naming conventions."""
        value = value.upper().strip()
        if not value.replace('_', '').isalnum():
            raise serializers.ValidationError(
                "Detail type code must contain only letters, numbers, and underscores"
            )
        return value

class AccountSerializer(serializers.ModelSerializer):
    """
    Serializer for Individual Accounts in Chart of Accounts
    
    This handles the core account information including:
    - Account number and name
    - Classification (account type and detail type)
    - Hierarchical parent/child relationships
    - Currency and balance information
    - Banking integration details
    
    Accounts are the foundation of all financial transactions and reporting.
    """
    
    # Related field display names
    account_type_name = serializers.CharField(source='account_type.name', read_only=True)
    detail_type_name = serializers.CharField(source='detail_type.name', read_only=True)
    parent_account_name = serializers.CharField(source='parent_account.account_name', read_only=True)
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    # Calculated fields
    current_balance = serializers.SerializerMethodField()
    full_account_path = serializers.SerializerMethodField()
    has_transactions = serializers.SerializerMethodField()
    
    # Nested child accounts
    child_accounts = serializers.SerializerMethodField()
    
    class Meta:
        model = Account
        fields = [
            'id', 'account_number', 'account_name', 'description',
            'account_type', 'account_type_name', 'detail_type', 'detail_type_name',
            'parent_account', 'parent_account_name', 'currency',
            'is_active', 'is_header_account', 'opening_balance', 'opening_balance_date',
            'tax_line', 'bank_account_number', 'bank_routing_number',
            'level', 'path',  # HYBRID OPTIMIZATION: Include hierarchy fields
            'current_balance', 'full_account_path', 'has_transactions',
            'child_accounts', 'created_by', 'created_by_username', 
            'created_at', 'updated_at', 'version'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'current_balance', 'full_account_path',
            'has_transactions', 'child_accounts', 'created_by',
            'level', 'path'  # These are auto-calculated, read-only
        ]
    
    def get_current_balance(self, obj):
        """Get the current balance of the account."""
        return str(obj.get_current_balance())
    
    def get_full_account_path(self, obj):
        """Get the hierarchical path of the account."""
        return obj.get_full_account_path()
    
    def get_has_transactions(self, obj):
        """Check if the account has any posted transactions."""
        return obj.journal_lines.filter(
            journal_entry__status='POSTED'
        ).exists()
    
    def get_child_accounts(self, obj):
        """Get immediate child accounts."""
        children = obj.child_accounts.filter(is_active=True).order_by('account_number')
        return AccountSerializer(children, many=True, context=self.context).data
    
    def validate_account_number(self, value):
        """Validate account number format and uniqueness."""
        value = value.strip().upper()
        
        # Basic format validation
        if not value:
            raise serializers.ValidationError("Account number is required")
        
        if len(value) < 3:
            raise serializers.ValidationError("Account number must be at least 3 characters")
        
        # Check for valid characters (letters, numbers, dashes, dots)
        if not all(c.isalnum() or c in '-.' for c in value):
            raise serializers.ValidationError(
                "Account number can only contain letters, numbers, dashes, and periods"
            )
        
        return value
    
    def validate_account_name(self, value):
        """Validate account name."""
        value = value.strip()
        if len(value) < 2:
            raise serializers.ValidationError("Account name must be at least 2 characters")
        return value
    
    def validate(self, data):
        """Cross-field validation for account setup."""
        
        # Ensure detail type belongs to the selected account type
        if data.get('detail_type') and data.get('account_type'):
            if data['detail_type'].account_type != data['account_type']:
                raise serializers.ValidationError({
                    'detail_type': f"Selected detail type belongs to {data['detail_type'].account_type.name} accounts, not {data['account_type'].name}"
                })
        
        # Header accounts cannot have opening balances
        if data.get('is_header_account') and data.get('opening_balance', 0) != 0:
            raise serializers.ValidationError({
                'opening_balance': "Header accounts cannot have opening balances"
            })
        
        # Parent account must be of same account type or a header account
        if data.get('parent_account'):
            parent = data['parent_account']
            if not parent.is_header_account and parent.account_type != data.get('account_type'):
                raise serializers.ValidationError({
                    'parent_account': "Parent account must be of the same account type or a header account"
                })
        
        return data

class JournalEntryLineSerializer(serializers.ModelSerializer):
    """
    Serializer for Journal Entry Lines (Individual Debits and Credits)
    
    Each line represents a single debit or credit to an account.
    Critical business rules enforced:
    - Only debit OR credit can have a value (not both)
    - Amount must be positive
    - Account must not be a header account
    - Proper tracking dimensions can be included
    - Sales tax can be applied at line level
    """
    
    # Account display information
    account_number = serializers.CharField(source='account.account_number', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    
    # Sales tax display information
    sales_tax_description = serializers.CharField(source='sales_tax.description', read_only=True)
    sales_tax_rate = serializers.DecimalField(source='sales_tax.rate', max_digits=6, decimal_places=3, read_only=True)
    
    # Calculated fields
    amount = serializers.SerializerMethodField()
    is_debit = serializers.SerializerMethodField()
    
    class Meta:
        model = JournalEntryLine
        fields = [
            'id', 'account', 'account_number', 'account_name',
            'debit_amount', 'credit_amount', 'amount', 'is_debit',
            'description', 'memo', 'customer', 'vendor', 'project',
            'department', 'location', 'line_number',
            'original_amount', 'original_currency',
            'sales_tax', 'sales_tax_description', 'sales_tax_rate',
            'sales_tax_amount', 'taxable_amount',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'amount', 'is_debit', 'sales_tax_description', 'sales_tax_rate']
    
    def get_amount(self, obj):
        """Get the absolute amount (debit or credit)."""
        return str(obj.get_amount())
    
    def get_is_debit(self, obj):
        """Check if this is a debit line."""
        return obj.is_debit()
    
    def validate(self, data):
        """Validate journal entry line data."""
        
        # Ensure only debit OR credit has a value
        debit = data.get('debit_amount', Decimal('0.00'))
        credit = data.get('credit_amount', Decimal('0.00'))
        
        if debit > 0 and credit > 0:
            raise serializers.ValidationError(
                "A line cannot have both debit and credit amounts"
            )
        
        if debit == 0 and credit == 0:
            raise serializers.ValidationError(
                "A line must have either a debit or credit amount"
            )
        
        # Validate account is not a header account
        if data.get('account') and data['account'].is_header_account:
            raise serializers.ValidationError({
                'account': "Cannot post transactions to header accounts"
            })
        
        return data

class JournalEntrySerializer(serializers.ModelSerializer):
    """
    Serializer for Journal Entries (Complete Accounting Transactions)
    
    Journal entries are the core of double-entry bookkeeping. Each entry:
    - Must have at least 2 lines (one debit, one credit minimum)
    - Total debits must equal total credits
    - Includes complete audit trail information
    - Supports different entry types and currencies
    - Maintains proper workflow status
    """
    
    # Nested journal entry lines
    journal_lines = JournalEntryLineSerializer(many=True)
    
    # User information
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    posted_by_username = serializers.CharField(source='posted_by.username', read_only=True)
    
    # Calculated fields
    total_debits = serializers.SerializerMethodField()
    total_credits = serializers.SerializerMethodField()
    is_balanced = serializers.SerializerMethodField()
    can_be_posted = serializers.SerializerMethodField()
    
    class Meta:
        model = JournalEntry
        fields = [
            'id', 'entry_number', 'transaction_date', 'posting_date',
            'entry_type', 'description', 'reference_number', 'memo',
            'status', 'currency', 'exchange_rate', 'reporting_currency_amount', 'reporting_exchange_rate',
            'source_document_type', 'source_document_id', 'source_document_reference',
            'reversed_entry', 'reversal_reason',
            'total_debits', 'total_credits', 'is_balanced', 'can_be_posted',
            'journal_lines', 'created_by', 'created_by_username',
            'posted_by', 'posted_by_username',
            'created_at', 'updated_at', 'posted_at'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'posted_at', 'posting_date',
            'total_debits', 'total_credits', 'is_balanced', 'can_be_posted',
            'reporting_currency_amount', 'reporting_exchange_rate',
            'created_by', 'posted_by'
        ]
    
    def get_total_debits(self, obj):
        """Calculate total debit amount."""
        return obj.get_total_debits()
    
    def get_total_credits(self, obj):
        """Calculate total credit amount."""
        return obj.get_total_credits()
    
    def get_is_balanced(self, obj):
        """Check if debits equal credits."""
        return obj.is_balanced()
    
    def get_can_be_posted(self, obj):
        """Check if entry can be posted."""
        return obj.can_be_posted()
    
    def get_functional_currency(self, obj):
        """Get company's functional currency."""
        functional_currency, _ = obj.get_company_currencies()
        return functional_currency
    
    def get_reporting_currency(self, obj):
        """Get company's reporting currency."""
        _, reporting_currency = obj.get_company_currencies()
        return reporting_currency
    
    def get_functional_currency_symbol(self, obj):
        """Get functional currency symbol."""
        try:
            from account.models import Company
            company = Company.objects.first()
            if company:
                return company.get_currency_symbol('functional')
            return '$'
        except:
            return '$'
    
    def get_reporting_currency_symbol(self, obj):
        """Get reporting currency symbol."""
        try:
            from account.models import Company
            company = Company.objects.first()
            if company:
                return company.get_currency_symbol('reporting')
            return '$'
        except:
            return '$'
    
    def validate_entry_number(self, value):
        """Validate journal entry number format."""
        value = value.strip()
        if not value:
            raise serializers.ValidationError("Entry number is required")
        return value
    
    def validate_transaction_date(self, value):
        """Validate transaction date."""
        # Allow future dates up to 30 days from today for post-dated transactions
        max_future_date = timezone.now().date() + timezone.timedelta(days=30)
        if value > max_future_date:
            raise serializers.ValidationError(
                "Transaction date cannot be more than 30 days in the future"
            )
        return value
    
    def validate_journal_lines(self, value):
        """Validate journal entry lines as a collection."""
        if len(value) < 2:
            raise serializers.ValidationError(
                "Journal entry must have at least 2 lines"
            )
        
        # Check that we have at least one debit and one credit
        has_debit = any(
            line.get('debit_amount', Decimal('0.00')) > 0 
            for line in value
        )
        has_credit = any(
            line.get('credit_amount', Decimal('0.00')) > 0 
            for line in value
        )
        
        if not has_debit or not has_credit:
            raise serializers.ValidationError(
                "Journal entry must have at least one debit and one credit line"
            )
        
        return value
    
    def validate(self, data):
        """Cross-field validation for journal entries."""
        
        # Calculate totals from lines
        lines = data.get('journal_lines', [])
        total_debits = sum(
            line.get('debit_amount', Decimal('0.00')) 
            for line in lines
        )
        total_credits = sum(
            line.get('credit_amount', Decimal('0.00')) 
            for line in lines
        )
        
        # Ensure debits equal credits (double-entry rule)
        if total_debits != total_credits:
            raise serializers.ValidationError(
                f"Total debits ({total_debits}) must equal total credits ({total_credits})"
            )
        
        return data
    
    @transaction.atomic
    def create(self, validated_data):
        """Create journal entry with lines in a database transaction."""
        
        # Extract lines data
        lines_data = validated_data.pop('journal_lines')
        
        # Create the journal entry
        journal_entry = JournalEntry.objects.create(**validated_data)
        
        # Set default currency if not provided
        journal_entry.set_default_currency()
        journal_entry.save()
        
        # Create journal entry lines
        for i, line_data in enumerate(lines_data, 1):
            line_data['line_number'] = i
            JournalEntryLine.objects.create(
                journal_entry=journal_entry,
                **line_data
            )
        
        # Update currency amounts after lines are created
        journal_entry.update_currency_amounts()
        journal_entry.save()
        
        return journal_entry
    
    @transaction.atomic
    def update(self, instance, validated_data):
        """Update journal entry and lines (only if not posted)."""
        
        if instance.status == 'POSTED':
            raise serializers.ValidationError(
                "Cannot modify posted journal entries"
            )
        
        # Extract lines data
        lines_data = validated_data.pop('journal_lines', None)
        
        # Update journal entry fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # Update lines if provided
        if lines_data is not None:
            # Delete existing lines
            instance.journal_lines.all().delete()
            
            # Create new lines
            for i, line_data in enumerate(lines_data, 1):
                line_data['line_number'] = i
                JournalEntryLine.objects.create(
                    journal_entry=instance,
                    **line_data
                )
        
        return instance

class JournalEntryPostSerializer(serializers.Serializer):
    """
    Serializer for posting journal entries to the general ledger.
    
    This handles the workflow of moving journal entries from draft/pending
    status to posted status, which makes them part of the official
    accounting records and financial statements.
    """
    
    confirm_posting = serializers.BooleanField(
        default=False,
        help_text="Confirmation that the user wants to post this entry"
    )
    posting_memo = serializers.CharField(
        max_length=500,
        required=False,
        allow_blank=True,
        help_text="Optional memo about the posting"
    )
    
    def validate_confirm_posting(self, value):
        """Ensure user has confirmed the posting action."""
        if not value:
            raise serializers.ValidationError(
                "You must confirm that you want to post this journal entry"
            )
        return value

class ProjectSerializer(serializers.ModelSerializer):
    """
    Serializer for Project/Job tracking
    
    Projects allow detailed tracking of transactions for better
    cost allocation and profitability analysis.
    """
    
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = Project
        fields = [
            'id', 'project_code', 'project_name', 'description',
            'start_date', 'end_date', 'is_active',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_by_username', 'created_at', 'updated_at']
    
    def validate_project_code(self, value):
        """Validate project code format."""
        value = value.strip().upper()
        if not value.replace('-', '').replace('_', '').isalnum():
            raise serializers.ValidationError(
                "Project code must contain only letters, numbers, hyphens, and underscores"
            )
        return value

class ProductSerializer(serializers.ModelSerializer):
    """
    Serializer for Product/Service tracking
    
    Products allow detailed tracking of transactions for better
    profitability analysis and inventory management.
    """
    
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    class Meta:
        model = Product
        fields = [
            'id', 'product_code', 'product_name', 'description',
            'product_type', 'unit_price', 'is_active',
            'created_by', 'created_by_username', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_by', 'created_by_username', 'created_at', 'updated_at']
    
    def validate_product_code(self, value):
        """Validate product code format."""
        value = value.strip().upper()
        if not value.replace('-', '').replace('_', '').isalnum():
            raise serializers.ValidationError(
                "Product code must contain only letters, numbers, hyphens, and underscores"
            )
        return value

class VoucherTypeSerializer(serializers.ModelSerializer):
    """
    Serializer for Voucher Types (JV, PV, RV, BV, CV)
    
    Voucher types define different forms for journal entries
    with specific numbering and behavior patterns.
    """
    
    class Meta:
        model = VoucherType
        fields = [
            'id', 'voucher_code', 'voucher_name', 'description',
            'number_prefix', 'next_number', 'is_active'
        ]

class SalesTaxSerializer(serializers.ModelSerializer):
    """
    Simple serializer for Sales Tax dropdown lists
    """
    
    class Meta:
        model = SalesTax
        fields = ['id', 'tax_type', 'description', 'rate']

class TDSSerializer(serializers.ModelSerializer):
    """
    Simple serializer for TDS dropdown lists
    """
    
    class Meta:
        model = TDS
        fields = ['id', 'tds_type', 'description', 'rate', 'section', 'threshold_limit']

class EnhancedJournalEntryLineSerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for Journal Entry Lines with product/project tracking and sales tax
    """
    
    # Account display information
    account_number = serializers.CharField(source='account.account_number', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    
    # Project and product information
    project_name = serializers.CharField(source='project.project_name', read_only=True)
    product_name = serializers.CharField(source='product.product_name', read_only=True)
    
    # Sales tax display information
    sales_tax_description = serializers.CharField(source='sales_tax.description', read_only=True)
    sales_tax_rate = serializers.DecimalField(source='sales_tax.rate', max_digits=6, decimal_places=3, read_only=True)
    
    # Calculated fields
    amount = serializers.SerializerMethodField()
    is_debit = serializers.SerializerMethodField()
    line_total = serializers.SerializerMethodField()
    
    class Meta:
        model = JournalEntryLine
        fields = [
            'id', 'account', 'account_number', 'account_name',
            'debit_amount', 'credit_amount', 'amount', 'is_debit',
            'description', 'memo', 'customer', 'vendor', 
            'project', 'project_name', 'product', 'product_name',
            'quantity', 'unit_price', 'line_total',
            'department', 'location', 'line_number',
            'original_amount', 'original_currency',
            'sales_tax', 'sales_tax_description', 'sales_tax_rate',
            'sales_tax_amount', 'taxable_amount',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'amount', 'is_debit', 'line_total', 'sales_tax_description', 'sales_tax_rate']
    
    def get_amount(self, obj):
        """Get the line amount (debit or credit)."""
        return str(obj.get_amount())
    
    def get_is_debit(self, obj):
        """Check if this is a debit line."""
        return obj.is_debit()
    
    def get_line_total(self, obj):
        """Calculate line total from quantity and unit price."""
        if obj.quantity and obj.unit_price:
            return str(obj.quantity * obj.unit_price)
        return None
    
    def validate(self, data):
        """Enhanced validation for journal entry lines."""
        # Existing validation
        debit = data.get('debit_amount', Decimal('0.00'))
        credit = data.get('credit_amount', Decimal('0.00'))
        
        if debit > 0 and credit > 0:
            raise serializers.ValidationError(
                "A line cannot have both debit and credit amounts. Use separate lines."
            )
        
        if debit == 0 and credit == 0:
            raise serializers.ValidationError(
                "A line must have either a debit or credit amount."
            )
        
        # Validate quantity and unit price consistency
        quantity = data.get('quantity')
        unit_price = data.get('unit_price')
        
        if quantity and not unit_price:
            raise serializers.ValidationError({
                'unit_price': 'Unit price is required when quantity is specified'
            })
        
        if unit_price and not quantity:
            raise serializers.ValidationError({
                'quantity': 'Quantity is required when unit price is specified'
            })
        
        # Check if amounts match calculated totals
        if quantity and unit_price:
            calculated_total = quantity * unit_price
            line_amount = debit if debit > 0 else credit
            
            if abs(calculated_total - line_amount) > Decimal('0.01'):  # Allow small rounding differences
                raise serializers.ValidationError(
                    f"Line amount ({line_amount}) doesn't match calculated total ({calculated_total})"
                )
        
        return data

class EnhancedJournalEntrySerializer(serializers.ModelSerializer):
    """
    Enhanced serializer for Journal Entries with tax calculations and voucher types
    """
    
    # Nested journal entry lines
    journal_lines = EnhancedJournalEntryLineSerializer(many=True)
    
    # Voucher type information
    voucher_type_name = serializers.CharField(source='voucher_type.voucher_name', read_only=True)
    voucher_code = serializers.CharField(source='voucher_type.voucher_code', read_only=True)
    
    # Tax information
    input_tax_description = serializers.CharField(source='input_tax.description', read_only=True)
    output_tax_description = serializers.CharField(source='output_tax.description', read_only=True)
    tds_description = serializers.CharField(source='tds_rate.description', read_only=True)
    
    # User information
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    posted_by_username = serializers.CharField(source='posted_by.username', read_only=True)
    
    # Calculated fields
    total_debits = serializers.SerializerMethodField()
    total_credits = serializers.SerializerMethodField()
    is_balanced = serializers.SerializerMethodField()
    can_be_posted = serializers.SerializerMethodField()
    calculated_net_amount = serializers.SerializerMethodField()
    
    class Meta:
        model = JournalEntry
        fields = [
            'id', 'entry_number', 'transaction_date', 'posting_date',
            'entry_type', 'voucher_type', 'voucher_type_name', 'voucher_code',
            'description', 'reference_number', 'memo', 'status', 'currency', 'exchange_rate',
            'gross_amount', 'sales_tax_amount', 'tds_amount', 'net_amount', 'calculated_net_amount',
            'input_tax', 'input_tax_description', 'output_tax', 'output_tax_description',
            'tds_rate', 'tds_description',
            'source_document_type', 'source_document_id', 'source_document_reference',
            'reversed_entry', 'reversal_reason',
            'total_debits', 'total_credits', 'is_balanced', 'can_be_posted',
            'journal_lines', 'created_by', 'created_by_username',
            'posted_by', 'posted_by_username',
            'created_at', 'updated_at', 'posted_at'
        ]
        read_only_fields = [
            'created_at', 'updated_at', 'total_debits', 'total_credits',
            'is_balanced', 'can_be_posted', 'created_by', 'posted_by',
            'posted_at', 'calculated_net_amount'
        ]
    
    def get_total_debits(self, obj):
        """Calculate total debit amount."""
        return obj.get_total_debits()
    
    def get_total_credits(self, obj):
        """Calculate total credit amount."""
        return obj.get_total_credits()
    
    def get_is_balanced(self, obj):
        """Check if debits equal credits."""
        return obj.is_balanced()
    
    def get_can_be_posted(self, obj):
        """Check if entry can be posted."""
        return obj.can_be_posted()
    
    def get_calculated_net_amount(self, obj):
        """Get calculated net amount based on tax rules."""
        obj.calculate_taxes()
        return str(obj.net_amount)
    
    def validate_entry_number(self, value):
        """Validate journal entry number format."""
        value = value.strip()
        if not value:
            raise serializers.ValidationError("Entry number is required")
        return value
    
    def validate_gross_amount(self, value):
        """Validate gross amount."""
        if value and value < 0:
            raise serializers.ValidationError("Gross amount cannot be negative")
        return value
    
    def validate(self, data):
        """Enhanced validation with tax calculations."""
        
        # Validate voucher type consistency
        voucher_type = data.get('voucher_type')
        input_tax = data.get('input_tax')
        output_tax = data.get('output_tax')
        
        if voucher_type:
            if voucher_type.voucher_code == 'PV' and output_tax:
                raise serializers.ValidationError({
                    'output_tax': 'Payment vouchers should use input tax, not output tax'
                })
            
            if voucher_type.voucher_code == 'RV' and input_tax:
                raise serializers.ValidationError({
                    'input_tax': 'Receipt vouchers should use output tax, not input tax'
                })
        
        # Validate TDS threshold
        tds_rate = data.get('tds_rate')
        gross_amount = data.get('gross_amount', Decimal('0.00'))
        
        if tds_rate and tds_rate.threshold_limit:
            if gross_amount < tds_rate.threshold_limit:
                raise serializers.ValidationError({
                    'gross_amount': f'Amount must be at least {tds_rate.threshold_limit} for this TDS rate'
                })
        
        return data
    
    @transaction.atomic
    def create(self, validated_data):
        """Create journal entry with tax calculations."""
        journal_lines_data = validated_data.pop('journal_lines')
        
        # Set the created_by field
        validated_data['created_by'] = self.context['request'].user
        
        # Generate entry number if not provided
        if not validated_data.get('entry_number'):
            voucher_type = validated_data.get('voucher_type')
            if voucher_type:
                validated_data['entry_number'] = voucher_type.get_next_voucher_number()
            else:
                # Generate a simple sequential number
                last_entry = JournalEntry.objects.order_by('-id').first()
                next_number = (last_entry.id + 1) if last_entry else 1
                validated_data['entry_number'] = f"JE{next_number:06d}"
        
        # Create the journal entry
        journal_entry = JournalEntry.objects.create(**validated_data)
        
        # Calculate taxes
        journal_entry.calculate_taxes()
        journal_entry.save()
        
        # Create journal entry lines
        for line_data in journal_lines_data:
            line_data['journal_entry'] = journal_entry
            JournalEntryLine.objects.create(**line_data)
        
        # Generate additional tax journal lines automatically
        tax_lines = journal_entry.generate_tax_journal_lines()
        for tax_line_data in tax_lines:
            tax_line_data['journal_entry'] = journal_entry
            JournalEntryLine.objects.create(**tax_line_data)
        
        return journal_entry
    
    @transaction.atomic
    def update(self, instance, validated_data):
        """Update journal entry with tax recalculation."""
        
        # Don't allow modification of posted entries
        if instance.status == 'POSTED':
            raise serializers.ValidationError(
                "Posted journal entries cannot be modified. Create a reversing entry instead."
            )
        
        journal_lines_data = validated_data.pop('journal_lines', None)
        
        # Update the journal entry fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Recalculate taxes
        instance.calculate_taxes()
        instance.save()
        
        # Update journal entry lines if provided
        if journal_lines_data is not None:
            # Delete existing lines
            instance.journal_lines.all().delete()
            
            # Create new lines
            for line_data in journal_lines_data:
                line_data['journal_entry'] = instance
                JournalEntryLine.objects.create(**line_data)
            
            # Generate additional tax journal lines automatically
            tax_lines = instance.generate_tax_journal_lines()
            for tax_line_data in tax_lines:
                tax_line_data['journal_entry'] = instance
                JournalEntryLine.objects.create(**tax_line_data)
        
        return instance

class RecurringJournalEntryLineSerializer(serializers.ModelSerializer):
    """
    Serializer for Recurring Journal Entry Lines (Template Lines)
    
    These define the individual account postings for recurring journal entry templates.
    Each line specifies which account to debit or credit and the amount.
    """
    
    # Account display information
    account_number = serializers.CharField(source='account.account_number', read_only=True)
    account_name = serializers.CharField(source='account.account_name', read_only=True)
    
    # Calculated fields
    amount = serializers.SerializerMethodField()
    is_debit = serializers.SerializerMethodField()
    
    class Meta:
        model = RecurringJournalEntryLine
        fields = [
            'id', 'account', 'account_number', 'account_name',
            'debit_amount', 'credit_amount', 'amount', 'is_debit',
            'description', 'memo', 'line_number',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'amount', 'is_debit']
    
    def get_amount(self, obj):
        """Get the amount (debit or credit) for this line."""
        return str(obj.debit_amount if obj.debit_amount > 0 else obj.credit_amount)
    
    def get_is_debit(self, obj):
        """Check if this line is a debit."""
        return obj.debit_amount > 0
    
    def validate(self, data):
        """Validate that line has either debit or credit amount, not both."""
        debit = data.get('debit_amount', Decimal('0.00'))
        credit = data.get('credit_amount', Decimal('0.00'))
        
        if debit > 0 and credit > 0:
            raise serializers.ValidationError(
                "A line cannot have both debit and credit amounts"
            )
        
        if debit == 0 and credit == 0:
            raise serializers.ValidationError(
                "A line must have either a debit or credit amount"
            )
        
        # Validate account exists and is not a header account
        account = data.get('account')
        if account and account.is_header_account:
            raise serializers.ValidationError({
                'account': "Cannot post to header accounts. Please select a detail account."
            })
        
        return data

class RecurringJournalEntrySerializer(serializers.ModelSerializer):
    """
    Serializer for Recurring Journal Entry Templates
    
    These templates allow users to create journal entries that repeat on a schedule
    (monthly rent, quarterly insurance, annual depreciation, etc.).
    """
    
    # Nested recurring lines
    recurring_lines = RecurringJournalEntryLineSerializer(many=True)
    
    # User information
    created_by_username = serializers.CharField(source='created_by.username', read_only=True)
    
    # Calculated fields
    total_debits = serializers.SerializerMethodField()
    total_credits = serializers.SerializerMethodField()
    is_balanced = serializers.SerializerMethodField()
    next_generation_date_display = serializers.SerializerMethodField()
    
    class Meta:
        model = RecurringJournalEntry
        fields = [
            'id', 'template_name', 'description', 'reference_number', 'memo',
            'frequency', 'start_date', 'end_date', 'next_generation_date',
            'next_generation_date_display', 'currency', 'is_active',
            'total_generated', 'last_generated_at',
            'total_debits', 'total_credits', 'is_balanced',
            'recurring_lines', 'created_by', 'created_by_username',
            'created_at', 'updated_at'
        ]
        read_only_fields = [
            'created_by', 'created_by_username', 'created_at', 'updated_at',
            'total_generated', 'last_generated_at', 'next_generation_date',
            'total_debits', 'total_credits', 'is_balanced', 'next_generation_date_display'
        ]
    
    def get_total_debits(self, obj):
        """Calculate total debit amount from all lines."""
        total = sum(line.debit_amount for line in obj.recurring_lines.all())
        return str(total)
    
    def get_total_credits(self, obj):
        """Calculate total credit amount from all lines."""
        total = sum(line.credit_amount for line in obj.recurring_lines.all())
        return str(total)
    
    def get_is_balanced(self, obj):
        """Check if total debits equal total credits."""
        total_debits = sum(line.debit_amount for line in obj.recurring_lines.all())
        total_credits = sum(line.credit_amount for line in obj.recurring_lines.all())
        return abs(total_debits - total_credits) < Decimal('0.01')
    
    def get_next_generation_date_display(self, obj):
        """Get formatted next generation date."""
        if obj.next_generation_date:
            return obj.next_generation_date.strftime('%Y-%m-%d')
        return None
    
    def validate_template_name(self, value):
        """Validate template name is unique and not empty."""
        value = value.strip()
        if not value:
            raise serializers.ValidationError("Template name is required")
        
        # Check for uniqueness (excluding current instance for updates)
        queryset = RecurringJournalEntry.objects.filter(template_name__iexact=value)
        if self.instance:
            queryset = queryset.exclude(pk=self.instance.pk)
        
        if queryset.exists():
            raise serializers.ValidationError(
                "A recurring template with this name already exists"
            )
        
        return value
    
    def validate_recurring_lines(self, value):
        """Validate recurring lines."""
        if len(value) < 2:
            raise serializers.ValidationError(
                "At least 2 recurring lines are required"
            )
        
        # Check that we have both debits and credits
        has_debit = any(line.get('debit_amount', 0) > 0 for line in value)
        has_credit = any(line.get('credit_amount', 0) > 0 for line in value)
        
        if not (has_debit and has_credit):
            raise serializers.ValidationError(
                "Template must have at least one debit line and one credit line"
            )
        
        return value
    
    def validate(self, data):
        """Cross-field validation for recurring templates."""
        
        # Validate date range
        start_date = data.get('start_date')
        end_date = data.get('end_date')
        
        if start_date and end_date and end_date <= start_date:
            raise serializers.ValidationError({
                'end_date': "End date must be after start date"
            })
        
        # Validate that template is balanced
        recurring_lines = data.get('recurring_lines', [])
        if recurring_lines:
            total_debits = sum(
                line.get('debit_amount', Decimal('0.00')) 
                for line in recurring_lines
            )
            total_credits = sum(
                line.get('credit_amount', Decimal('0.00')) 
                for line in recurring_lines
            )
            
            if abs(total_debits - total_credits) >= Decimal('0.01'):
                raise serializers.ValidationError(
                    "Total debits must equal total credits in the template"
                )
        
        return data
    
    @transaction.atomic
    def create(self, validated_data):
        """Create recurring template with lines."""
        recurring_lines_data = validated_data.pop('recurring_lines')
        
        # Set created_by from request user
        validated_data['created_by'] = self.context['request'].user
        
        # Calculate next generation date
        recurring_entry = RecurringJournalEntry.objects.create(**validated_data)
        recurring_entry.calculate_next_generation_date()
        recurring_entry.save()
        
        # Create recurring lines
        for line_data in recurring_lines_data:
            RecurringJournalEntryLine.objects.create(
                recurring_entry=recurring_entry,
                **line_data
            )
        
        return recurring_entry
    
    @transaction.atomic
    def update(self, instance, validated_data):
        """Update recurring template and lines."""
        recurring_lines_data = validated_data.pop('recurring_lines', None)
        
        # Update main template fields
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        
        # Recalculate next generation date if frequency or start date changed
        if 'frequency' in validated_data or 'start_date' in validated_data:
            instance.calculate_next_generation_date()
        
        instance.save()
        
        # Update recurring lines if provided
        if recurring_lines_data is not None:
            # Delete existing lines
            instance.recurring_lines.all().delete()
            
            # Create new lines
            for line_data in recurring_lines_data:
                RecurringJournalEntryLine.objects.create(
                    recurring_entry=instance,
                    **line_data
                )
        
        return instance


# Odoo-style Serializers

class AccountMoveLineSerializer(serializers.ModelSerializer):
    """
    Odoo-style AccountMoveLine Serializer
    """
    account_number = serializers.CharField(source='account_id.account_number', read_only=True)
    account_name = serializers.CharField(source='account_id.account_name', read_only=True)
    partner_name = serializers.CharField(source='partner_id.name', read_only=True)
    product_name = serializers.CharField(source='product_id.product_name', read_only=True)

    class Meta:
        model = AccountMoveLine
        fields = [
            'id', 'move_id', 'account_id', 'account_number', 'account_name',
            'partner_id', 'partner_name', 'name', 'debit', 'credit', 'balance',
            'currency_id', 'amount_currency', 'tax_ids', 'tax_base_amount',
            'product_id', 'product_name', 'quantity', 'price_unit',
            'reconciled', 'full_reconcile_id', 'company_id',
            'create_date', 'write_date'
        ]
        read_only_fields = ['id', 'balance', 'create_date', 'write_date']
        extra_kwargs = {
            'account_id': {'required': True, 'allow_null': False},
            'name': {'required': True, 'allow_blank': False},
        }

    def validate(self, data):
        """
        Validate that line has either debit or credit, not both
        """
        # Convert string values to Decimal for validation
        debit = Decimal(str(data.get('debit', '0.00')))
        credit = Decimal(str(data.get('credit', '0.00')))

        if debit > 0 and credit > 0:
            raise serializers.ValidationError(
                "Line cannot have both debit and credit amounts"
            )

        if debit == 0 and credit == 0:
            raise serializers.ValidationError(
                "Line must have either a debit or credit amount"
            )

        return data


class AccountMoveSerializer(serializers.ModelSerializer):
    """
    Odoo-style AccountMove Serializer
    """
    partner_name = serializers.CharField(source='partner_id.name', read_only=True)
    journal_name = serializers.CharField(source='journal_id.name', read_only=True)
    line_ids = AccountMoveLineSerializer(many=True, required=False)

    class Meta:
        model = AccountMove
        fields = [
            'id', 'name', 'move_type', 'state', 'ref', 'date',
            'partner_id', 'partner_name', 'amount_total', 'amount_residual',
            'currency_id', 'journal_id', 'journal_name', 'company_id',
            'sequence_id', 'line_ids', 'created_uid', 'write_uid',
            'create_date', 'write_date'
        ]
        read_only_fields = [
            'id', 'name', 'amount_total', 'amount_residual',
            'created_uid', 'write_uid', 'create_date', 'write_date'
        ]

    def create(self, validated_data):
        """
        Create AccountMove with lines
        """
        line_ids_data = validated_data.pop('line_ids', [])

        with transaction.atomic():
            # Create the move
            move = AccountMove.objects.create(**validated_data)

            # Create move lines
            for line_data in line_ids_data:
                try:
                    AccountMoveLine.objects.create(move_id=move, **line_data)
                except Exception as e:
                    raise serializers.ValidationError(f"Error creating move line: {str(e)}")

            # Calculate totals
            try:
                move._calculate_totals()
            except Exception as e:
                # If calculation fails, just set basic totals
                move.amount_total = sum(
                    max(line.get('debit', 0), line.get('credit', 0))
                    for line in line_ids_data
                )
                move.amount_residual = move.amount_total
                move.save()

            return move

    def update(self, instance, validated_data):
        """
        Update AccountMove with lines
        """
        line_ids_data = validated_data.pop('line_ids', None)

        with transaction.atomic():
            # Update move fields
            for attr, value in validated_data.items():
                setattr(instance, attr, value)
            instance.save()

            # Update lines if provided
            if line_ids_data is not None:
                # Delete existing lines
                instance.line_ids.all().delete()

                # Create new lines
                for line_data in line_ids_data:
                    AccountMoveLine.objects.create(move_id=instance, **line_data)

                # Recalculate totals
                instance._calculate_totals()

            return instance

    def validate(self, data):
        """
        Validate AccountMove data
        """
        # Check if move can be modified
        if self.instance and self.instance.state == 'posted':
            # Only allow certain fields to be modified on posted moves
            allowed_fields = ['ref']
            for field in data.keys():
                if field not in allowed_fields:
                    raise serializers.ValidationError(
                        f"Cannot modify {field} on posted moves"
                    )

        # Validate that we have line_ids for new moves
        line_ids_data = data.get('line_ids', [])
        if not self.instance and not line_ids_data:
            raise serializers.ValidationError(
                "Account move must have at least one line"
            )

        return data


class AccountJournalSerializer(serializers.ModelSerializer):
    """
    Odoo-style AccountJournal Serializer
    """
    default_account_name = serializers.CharField(
        source='default_account_id.account_name',
        read_only=True
    )

    class Meta:
        model = AccountJournal
        fields = [
            'id', 'name', 'code', 'type', 'default_account_id',
            'default_account_name', 'sequence_id', 'active',
            'company_id', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_code(self, value):
        """
        Validate journal code is unique
        """
        if self.instance:
            # Updating existing journal
            if AccountJournal.objects.exclude(id=self.instance.id).filter(code=value).exists():
                raise serializers.ValidationError("Journal code must be unique")
        else:
            # Creating new journal
            if AccountJournal.objects.filter(code=value).exists():
                raise serializers.ValidationError("Journal code must be unique")

        return value


class IrSequenceSerializer(serializers.ModelSerializer):
    """
    Odoo-style IrSequence Serializer
    """

    class Meta:
        model = IrSequence
        fields = [
            'id', 'name', 'code', 'prefix', 'suffix', 'number_next',
            'number_increment', 'padding', 'use_date_range', 'active',
            'company_id', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']

    def validate_code(self, value):
        """
        Validate sequence code is unique
        """
        if self.instance:
            # Updating existing sequence
            if IrSequence.objects.exclude(id=self.instance.id).filter(code=value).exists():
                raise serializers.ValidationError("Sequence code must be unique")
        else:
            # Creating new sequence
            if IrSequence.objects.filter(code=value).exists():
                raise serializers.ValidationError("Sequence code must be unique")

        return value